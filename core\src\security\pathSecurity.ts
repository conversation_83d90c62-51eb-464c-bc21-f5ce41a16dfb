// Path security and validation utilities
import { resolve, normalize, relative, sep, join } from 'path';
import { stat, access, readlink } from 'fs/promises';
import { constants } from 'fs';
import { logger } from '../core/logger.js';
import { ArienError, ErrorCode } from '../utils/errors.js';

export interface PathValidationResult {
  isValid: boolean;
  normalizedPath: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  issues: PathSecurityIssue[];
  permissions: PathPermissions;
}

export interface PathSecurityIssue {
  type: 'traversal' | 'permission' | 'system' | 'symlink' | 'hidden' | 'suspicious';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  suggestion?: string;
}

export interface PathPermissions {
  readable: boolean;
  writable: boolean;
  executable: boolean;
  exists: boolean;
  isDirectory: boolean;
  isSymlink: boolean;
}

export interface PathSecurityConfig {
  allowedRoots: string[];
  blockedPaths: string[];
  systemPaths: string[];
  allowHiddenFiles: boolean;
  allowSymlinks: boolean;
  maxSymlinkDepth: number;
  allowTraversal: boolean;
  trustedDirectories: string[];
}

export class PathSecurity {
  private config: PathSecurityConfig;

  constructor(config: Partial<PathSecurityConfig> = {}) {
    this.config = {
      allowedRoots: [process.cwd()],
      blockedPaths: [
        '/etc/passwd',
        '/etc/shadow',
        '/etc/sudoers',
        '/proc',
        '/sys',
        '/dev',
        'C:\\Windows\\System32',
        'C:\\Windows\\SysWOW64'
      ],
      systemPaths: [
        '/bin',
        '/sbin',
        '/usr/bin',
        '/usr/sbin',
        '/etc',
        '/var',
        '/sys',
        '/proc',
        '/boot',
        'C:\\Windows',
        'C:\\Program Files',
        'C:\\Program Files (x86)'
      ],
      allowHiddenFiles: false,
      allowSymlinks: true,
      maxSymlinkDepth: 5,
      allowTraversal: false,
      trustedDirectories: [],
      ...config
    };
  }

  public async validatePath(inputPath: string, operation: 'read' | 'write' | 'execute' | 'delete' = 'read'): Promise<PathValidationResult> {
    const issues: PathSecurityIssue[] = [];
    let riskLevel: PathValidationResult['riskLevel'] = 'low';

    try {
      // Normalize the path
      const normalizedPath = this.normalizePath(inputPath);

      // Check for path traversal
      this.checkPathTraversal(inputPath, normalizedPath, issues);

      // Check against blocked paths
      this.checkBlockedPaths(normalizedPath, issues);

      // Check system paths
      this.checkSystemPaths(normalizedPath, operation, issues);

      // Check hidden files
      this.checkHiddenFiles(normalizedPath, issues);

      // Check allowed roots
      this.checkAllowedRoots(normalizedPath, issues);

      // Get path permissions
      const permissions = await this.getPathPermissions(normalizedPath);

      // Check symlinks
      if (permissions.isSymlink) {
        await this.checkSymlinks(normalizedPath, issues);
      }

      // Check operation permissions
      this.checkOperationPermissions(operation, permissions, issues);

      // Calculate risk level
      riskLevel = this.calculateRiskLevel(issues, operation);

      return {
        isValid: !issues.some(issue => issue.severity === 'critical'),
        normalizedPath,
        riskLevel,
        issues,
        permissions
      };
    } catch (error) {
      logger.error('Path validation failed', { path: inputPath, error });
      
      return {
        isValid: false,
        normalizedPath: inputPath,
        riskLevel: 'critical',
        issues: [{
          type: 'system',
          severity: 'critical',
          message: `Path validation failed: ${error instanceof Error ? error.message : String(error)}`
        }],
        permissions: {
          readable: false,
          writable: false,
          executable: false,
          exists: false,
          isDirectory: false,
          isSymlink: false
        }
      };
    }
  }

  private normalizePath(inputPath: string): string {
    try {
      // Resolve relative paths and normalize
      const resolved = resolve(inputPath);
      return normalize(resolved);
    } catch (error) {
      throw new ArienError(
        ErrorCode.INVALID_PATH,
        `Failed to normalize path: ${inputPath}`,
        { originalPath: inputPath, error }
      );
    }
  }

  private checkPathTraversal(originalPath: string, normalizedPath: string, issues: PathSecurityIssue[]): void {
    // Check for directory traversal patterns
    const traversalPatterns = ['../', '..\\', '%2e%2e%2f', '%2e%2e%5c'];
    
    for (const pattern of traversalPatterns) {
      if (originalPath.includes(pattern)) {
        issues.push({
          type: 'traversal',
          severity: this.config.allowTraversal ? 'medium' : 'critical',
          message: `Path traversal pattern detected: ${pattern}`,
          suggestion: 'Use absolute paths or ensure the path stays within allowed boundaries'
        });
      }
    }

    // Check if normalized path escapes allowed roots
    if (!this.config.allowTraversal) {
      const isWithinAllowedRoot = this.config.allowedRoots.some(root => {
        const relativePath = relative(root, normalizedPath);
        return !relativePath.startsWith('..') && !relativePath.startsWith('/');
      });

      if (!isWithinAllowedRoot) {
        issues.push({
          type: 'traversal',
          severity: 'critical',
          message: 'Path escapes allowed root directories',
          suggestion: 'Ensure the path stays within the project directory'
        });
      }
    }
  }

  private checkBlockedPaths(normalizedPath: string, issues: PathSecurityIssue[]): void {
    for (const blockedPath of this.config.blockedPaths) {
      if (normalizedPath.startsWith(blockedPath) || normalizedPath === blockedPath) {
        issues.push({
          type: 'system',
          severity: 'critical',
          message: `Access to blocked path: ${blockedPath}`,
          suggestion: 'This path is explicitly blocked for security reasons'
        });
      }
    }
  }

  private checkSystemPaths(normalizedPath: string, operation: string, issues: PathSecurityIssue[]): void {
    for (const systemPath of this.config.systemPaths) {
      if (normalizedPath.startsWith(systemPath)) {
        const severity = operation === 'read' ? 'medium' : 'high';
        issues.push({
          type: 'system',
          severity,
          message: `${operation} operation on system path: ${systemPath}`,
          suggestion: 'System paths require elevated permissions and careful handling'
        });
      }
    }
  }

  private checkHiddenFiles(normalizedPath: string, issues: PathSecurityIssue[]): void {
    if (!this.config.allowHiddenFiles) {
      const pathParts = normalizedPath.split(sep);
      const hasHiddenParts = pathParts.some(part => part.startsWith('.') && part !== '.' && part !== '..');
      
      if (hasHiddenParts) {
        issues.push({
          type: 'hidden',
          severity: 'medium',
          message: 'Access to hidden file or directory',
          suggestion: 'Hidden files may contain sensitive configuration data'
        });
      }
    }
  }

  private checkAllowedRoots(normalizedPath: string, issues: PathSecurityIssue[]): void {
    if (this.config.allowedRoots.length === 0) return;

    const isWithinAllowedRoot = this.config.allowedRoots.some(root => {
      return normalizedPath.startsWith(normalize(root));
    });

    if (!isWithinAllowedRoot) {
      issues.push({
        type: 'permission',
        severity: 'high',
        message: 'Path is outside allowed root directories',
        suggestion: 'Ensure the path is within the allowed project boundaries'
      });
    }
  }

  private async checkSymlinks(normalizedPath: string, issues: PathSecurityIssue[]): Promise<void> {
    if (!this.config.allowSymlinks) {
      issues.push({
        type: 'symlink',
        severity: 'medium',
        message: 'Symbolic link detected',
        suggestion: 'Symbolic links are not allowed in current security configuration'
      });
      return;
    }

    try {
      // Check symlink depth
      let currentPath = normalizedPath;
      let depth = 0;

      while (depth < this.config.maxSymlinkDepth) {
        try {
          const stats = await stat(currentPath);
          if (!stats.isSymbolicLink()) break;

          const linkTarget = await readlink(currentPath);
          currentPath = resolve(currentPath, '..', linkTarget);
          depth++;
        } catch (error) {
          break;
        }
      }

      if (depth >= this.config.maxSymlinkDepth) {
        issues.push({
          type: 'symlink',
          severity: 'high',
          message: `Symlink chain exceeds maximum depth (${this.config.maxSymlinkDepth})`,
          suggestion: 'Symlink chains may indicate circular references or security issues'
        });
      }

      // Check if symlink target is within allowed boundaries
      const finalTarget = await this.resolveSymlinkChain(normalizedPath);
      if (finalTarget !== normalizedPath) {
        const targetValidation = await this.validatePath(finalTarget, 'read');
        if (!targetValidation.isValid) {
          issues.push({
            type: 'symlink',
            severity: 'high',
            message: 'Symlink target violates security policies',
            suggestion: 'Symlink points to a restricted location'
          });
        }
      }
    } catch (error) {
      issues.push({
        type: 'symlink',
        severity: 'medium',
        message: 'Failed to validate symlink',
        suggestion: 'Unable to verify symlink target safety'
      });
    }
  }

  private async resolveSymlinkChain(path: string): Promise<string> {
    let currentPath = path;
    let depth = 0;

    while (depth < this.config.maxSymlinkDepth) {
      try {
        const stats = await stat(currentPath);
        if (!stats.isSymbolicLink()) break;

        const linkTarget = await readlink(currentPath);
        currentPath = resolve(currentPath, '..', linkTarget);
        depth++;
      } catch (error) {
        break;
      }
    }

    return currentPath;
  }

  private async getPathPermissions(path: string): Promise<PathPermissions> {
    try {
      const stats = await stat(path);
      
      // Test permissions
      let readable = false;
      let writable = false;
      let executable = false;

      try {
        await access(path, constants.R_OK);
        readable = true;
      } catch {}

      try {
        await access(path, constants.W_OK);
        writable = true;
      } catch {}

      try {
        await access(path, constants.X_OK);
        executable = true;
      } catch {}

      return {
        readable,
        writable,
        executable,
        exists: true,
        isDirectory: stats.isDirectory(),
        isSymlink: stats.isSymbolicLink()
      };
    } catch (error) {
      return {
        readable: false,
        writable: false,
        executable: false,
        exists: false,
        isDirectory: false,
        isSymlink: false
      };
    }
  }

  private checkOperationPermissions(
    operation: string,
    permissions: PathPermissions,
    issues: PathSecurityIssue[]
  ): void {
    if (!permissions.exists && operation !== 'write') {
      issues.push({
        type: 'permission',
        severity: 'medium',
        message: 'Path does not exist',
        suggestion: 'Verify the path is correct'
      });
      return;
    }

    switch (operation) {
      case 'read':
        if (permissions.exists && !permissions.readable) {
          issues.push({
            type: 'permission',
            severity: 'high',
            message: 'Insufficient read permissions',
            suggestion: 'Check file permissions or run with appropriate privileges'
          });
        }
        break;

      case 'write':
        if (permissions.exists && !permissions.writable) {
          issues.push({
            type: 'permission',
            severity: 'high',
            message: 'Insufficient write permissions',
            suggestion: 'Check file permissions or run with appropriate privileges'
          });
        }
        break;

      case 'execute':
        if (permissions.exists && !permissions.executable) {
          issues.push({
            type: 'permission',
            severity: 'high',
            message: 'Insufficient execute permissions',
            suggestion: 'Check file permissions or run with appropriate privileges'
          });
        }
        break;

      case 'delete':
        if (permissions.exists && !permissions.writable) {
          issues.push({
            type: 'permission',
            severity: 'high',
            message: 'Insufficient permissions to delete',
            suggestion: 'Check directory permissions or run with appropriate privileges'
          });
        }
        break;
    }
  }

  private calculateRiskLevel(issues: PathSecurityIssue[], operation: string): PathValidationResult['riskLevel'] {
    if (issues.some(issue => issue.severity === 'critical')) {
      return 'critical';
    }

    const highIssues = issues.filter(issue => issue.severity === 'high').length;
    if (highIssues >= 2) {
      return 'critical';
    } else if (highIssues >= 1) {
      return operation === 'write' || operation === 'delete' ? 'critical' : 'high';
    }

    const mediumIssues = issues.filter(issue => issue.severity === 'medium').length;
    if (mediumIssues >= 3) {
      return 'high';
    } else if (mediumIssues >= 1) {
      return 'medium';
    }

    return 'low';
  }

  public isTrustedPath(path: string): boolean {
    const normalizedPath = this.normalizePath(path);
    return this.config.trustedDirectories.some(trusted => 
      normalizedPath.startsWith(normalize(trusted))
    );
  }

  public addTrustedDirectory(directory: string): void {
    const normalizedDir = normalize(directory);
    if (!this.config.trustedDirectories.includes(normalizedDir)) {
      this.config.trustedDirectories.push(normalizedDir);
    }
  }

  public removeTrustedDirectory(directory: string): void {
    const normalizedDir = normalize(directory);
    const index = this.config.trustedDirectories.indexOf(normalizedDir);
    if (index >= 0) {
      this.config.trustedDirectories.splice(index, 1);
    }
  }

  public addAllowedRoot(root: string): void {
    const normalizedRoot = normalize(root);
    if (!this.config.allowedRoots.includes(normalizedRoot)) {
      this.config.allowedRoots.push(normalizedRoot);
    }
  }

  public removeAllowedRoot(root: string): void {
    const normalizedRoot = normalize(root);
    const index = this.config.allowedRoots.indexOf(normalizedRoot);
    if (index >= 0) {
      this.config.allowedRoots.splice(index, 1);
    }
  }

  public getConfig(): PathSecurityConfig {
    return { ...this.config };
  }

  public updateConfig(updates: Partial<PathSecurityConfig>): void {
    this.config = { ...this.config, ...updates };
  }
}

// Global path security instance
let globalPathSecurity: PathSecurity | null = null;

export function getPathSecurity(): PathSecurity {
  if (!globalPathSecurity) {
    globalPathSecurity = new PathSecurity();
  }
  return globalPathSecurity;
}

export function setPathSecurity(pathSecurity: PathSecurity): void {
  globalPathSecurity = pathSecurity;
}

// Utility functions
export async function validatePath(
  path: string,
  operation: 'read' | 'write' | 'execute' | 'delete' = 'read'
): Promise<PathValidationResult> {
  const pathSecurity = getPathSecurity();
  return pathSecurity.validatePath(path, operation);
}

export function isTrustedPath(path: string): boolean {
  const pathSecurity = getPathSecurity();
  return pathSecurity.isTrustedPath(path);
}
