{"name": "@arien/core", "version": "1.0.0", "description": "Core business logic and AI integration for Arien CLI", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "vitest", "test:watch": "vitest --watch", "lint": "eslint src --ext .ts,.tsx", "clean": "rm -rf dist"}, "dependencies": {"@ai-sdk/anthropic": "^0.0.39", "@ai-sdk/google": "^0.0.37", "@ai-sdk/openai": "^0.0.46", "@types/diff": "^7.0.2", "ai": "^3.3.0", "axios": "^1.6.0", "chalk": "^5.3.0", "commander": "^11.0.0", "diff": "^8.0.2", "dotenv": "^16.3.0", "fast-glob": "^3.3.0", "fs-extra": "^11.2.0", "ignore": "^5.3.0", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "minimatch": "^9.0.0", "node-fetch": "^3.3.0", "ora": "^7.0.0", "semver": "^7.5.0", "simple-git": "^3.20.0", "strip-ansi": "^7.1.0", "uuid": "^9.0.0", "winston": "^3.11.0", "zod": "^3.22.0"}, "devDependencies": {"@types/fs-extra": "^11.0.0", "@types/js-yaml": "^4.0.0", "@types/lodash": "^4.14.0", "@types/node": "^20.0.0", "@types/semver": "^7.5.0", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "typescript": "^5.0.0", "vitest": "^1.0.0"}, "keywords": ["ai", "cli", "core", "deepseek", "openai", "anthropic", "google"], "author": "Arien CLI Team", "license": "MIT"}