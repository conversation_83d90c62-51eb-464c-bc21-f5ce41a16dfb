// Git integration service
import { simpleGit, Simple<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Log<PERSON><PERSON><PERSON>, Diff<PERSON><PERSON><PERSON> } from 'simple-git';
import * as path from 'path';
import * as fs from 'fs-extra';
import { logger } from '../core/logger.js';
import { <PERSON><PERSON>Error, ErrorCode } from '../utils/errors.js';
import { LruCache } from '../utils/LruCache.js';

export interface GitStatus {
  isRepository: boolean;
  branch: string;
  ahead: number;
  behind: number;
  staged: string[];
  modified: string[];
  untracked: string[];
  deleted: string[];
  renamed: string[];
  conflicted: string[];
  clean: boolean;
}

export interface GitCommit {
  hash: string;
  date: Date;
  message: string;
  author: string;
  email: string;
  refs?: string;
}

export interface GitDiff {
  file: string;
  changes: number;
  insertions: number;
  deletions: number;
  diff: string;
}

export interface GitBranch {
  name: string;
  current: boolean;
  remote?: string;
  ahead?: number;
  behind?: number;
}

export interface GitRemote {
  name: string;
  url: string;
  type: 'fetch' | 'push';
}

export class GitService {
  private git: SimpleGit;
  private statusCache = new LruCache<string, GitStatus>(10);
  private branchCache = new LruCache<string, GitBranch[]>(10);
  private cacheTimeout = 30000; // 30 seconds

  constructor(private workingDirectory: string = process.cwd()) {
    this.git = simpleGit(workingDirectory);
  }

  public async isGitRepository(directory?: string): Promise<boolean> {
    try {
      const targetDir = directory || this.workingDirectory;
      const git = directory ? simpleGit(directory) : this.git;
      
      await git.status();
      return true;
    } catch (error) {
      return false;
    }
  }

  public async getStatus(useCache: boolean = true): Promise<GitStatus> {
    const cacheKey = this.workingDirectory;
    
    if (useCache) {
      const cached = this.statusCache.get(cacheKey);
      if (cached) {
        return cached;
      }
    }

    try {
      const isRepo = await this.isGitRepository();
      if (!isRepo) {
        const status: GitStatus = {
          isRepository: false,
          branch: '',
          ahead: 0,
          behind: 0,
          staged: [],
          modified: [],
          untracked: [],
          deleted: [],
          renamed: [],
          conflicted: [],
          clean: true
        };
        return status;
      }

      const statusResult = await this.git.status();
      const branch = await this.getCurrentBranch();
      const tracking = await this.getTrackingInfo();

      const status: GitStatus = {
        isRepository: true,
        branch,
        ahead: tracking.ahead,
        behind: tracking.behind,
        staged: statusResult.staged,
        modified: statusResult.modified,
        untracked: statusResult.not_added,
        deleted: statusResult.deleted,
        renamed: statusResult.renamed.map(r => `${r.from} -> ${r.to}`),
        conflicted: statusResult.conflicted,
        clean: statusResult.isClean()
      };

      this.statusCache.set(cacheKey, status);
      return status;
    } catch (error) {
      logger.error('Failed to get git status', { error });
      throw new ArienError(
        ErrorCode.TOOL_EXECUTION_FAILED,
        `Failed to get git status: ${error instanceof Error ? error.message : String(error)}`,
        { workingDirectory: this.workingDirectory, error }
      );
    }
  }

  public async getCurrentBranch(): Promise<string> {
    try {
      const branch = await this.git.branch();
      return branch.current || 'HEAD';
    } catch (error) {
      logger.error('Failed to get current branch', { error });
      return 'unknown';
    }
  }

  public async getBranches(useCache: boolean = true): Promise<GitBranch[]> {
    const cacheKey = this.workingDirectory;
    
    if (useCache) {
      const cached = this.branchCache.get(cacheKey);
      if (cached) {
        return cached;
      }
    }

    try {
      const branchResult = await this.git.branch(['-a']);
      const branches: GitBranch[] = [];

      for (const [name, branch] of Object.entries(branchResult.branches)) {
        if (name === branchResult.current) continue; // Skip current branch entry
        
        branches.push({
          name: name.replace('remotes/', ''),
          current: name === branchResult.current,
          remote: name.startsWith('remotes/') ? name.split('/')[1] : undefined
        });
      }

      // Add current branch
      if (branchResult.current) {
        const currentBranch = branchResult.branches[branchResult.current];
        branches.unshift({
          name: branchResult.current,
          current: true,
          ahead: 0, // Would need separate git command to get ahead/behind info
          behind: 0
        });
      }

      this.branchCache.set(cacheKey, branches);
      return branches;
    } catch (error) {
      logger.error('Failed to get branches', { error });
      throw new ArienError(
        ErrorCode.TOOL_EXECUTION_FAILED,
        `Failed to get git branches: ${error instanceof Error ? error.message : String(error)}`,
        { workingDirectory: this.workingDirectory, error }
      );
    }
  }

  public async getCommitHistory(
    limit: number = 10,
    branch?: string
  ): Promise<GitCommit[]> {
    try {
      const options: any = { maxCount: limit };
      if (branch) {
        options.from = branch;
      }

      const log = await this.git.log(options);
      
      return log.all.map(commit => ({
        hash: commit.hash,
        date: new Date(commit.date),
        message: commit.message,
        author: commit.author_name,
        email: commit.author_email,
        refs: commit.refs
      }));
    } catch (error) {
      logger.error('Failed to get commit history', { error });
      throw new ArienError(
        ErrorCode.TOOL_EXECUTION_FAILED,
        `Failed to get git history: ${error instanceof Error ? error.message : String(error)}`,
        { limit, branch, error }
      );
    }
  }

  public async getDiff(
    staged: boolean = false,
    file?: string
  ): Promise<GitDiff[]> {
    try {
      const options: string[] = [];
      if (staged) {
        options.push('--cached');
      }
      if (file) {
        options.push(file);
      }

      const diffResult = await this.git.diff(options);
      
      // Parse diff output (simplified)
      const diffs: GitDiff[] = [];
      const lines = diffResult.split('\n');
      let currentFile = '';
      let currentDiff = '';
      let insertions = 0;
      let deletions = 0;

      for (const line of lines) {
        if (line.startsWith('diff --git')) {
          if (currentFile) {
            diffs.push({
              file: currentFile,
              changes: insertions + deletions,
              insertions,
              deletions,
              diff: currentDiff
            });
          }
          currentFile = line.split(' b/')[1] || '';
          currentDiff = line + '\n';
          insertions = 0;
          deletions = 0;
        } else {
          currentDiff += line + '\n';
          if (line.startsWith('+') && !line.startsWith('+++')) {
            insertions++;
          } else if (line.startsWith('-') && !line.startsWith('---')) {
            deletions++;
          }
        }
      }

      if (currentFile) {
        diffs.push({
          file: currentFile,
          changes: insertions + deletions,
          insertions,
          deletions,
          diff: currentDiff
        });
      }

      return diffs;
    } catch (error) {
      logger.error('Failed to get git diff', { error });
      throw new ArienError(
        ErrorCode.TOOL_EXECUTION_FAILED,
        `Failed to get git diff: ${error instanceof Error ? error.message : String(error)}`,
        { staged, file, error }
      );
    }
  }

  public async getRemotes(): Promise<GitRemote[]> {
    try {
      const remotes = await this.git.getRemotes(true);
      
      return remotes.map(remote => ({
        name: remote.name,
        url: remote.refs.fetch || remote.refs.push || '',
        type: 'fetch' as const
      }));
    } catch (error) {
      logger.error('Failed to get remotes', { error });
      throw new ArienError(
        ErrorCode.TOOL_EXECUTION_FAILED,
        `Failed to get git remotes: ${error instanceof Error ? error.message : String(error)}`,
        { error }
      );
    }
  }

  public async stageFiles(files: string[]): Promise<void> {
    try {
      await this.git.add(files);
      this.invalidateCache();
      logger.info('Files staged successfully', { files });
    } catch (error) {
      logger.error('Failed to stage files', { files, error });
      throw new ArienError(
        ErrorCode.TOOL_EXECUTION_FAILED,
        `Failed to stage files: ${error instanceof Error ? error.message : String(error)}`,
        { files, error }
      );
    }
  }

  public async unstageFiles(files: string[]): Promise<void> {
    try {
      await this.git.reset(['HEAD', ...files]);
      this.invalidateCache();
      logger.info('Files unstaged successfully', { files });
    } catch (error) {
      logger.error('Failed to unstage files', { files, error });
      throw new ArienError(
        ErrorCode.TOOL_EXECUTION_FAILED,
        `Failed to unstage files: ${error instanceof Error ? error.message : String(error)}`,
        { files, error }
      );
    }
  }

  public async commit(message: string, files?: string[]): Promise<string> {
    try {
      if (files && files.length > 0) {
        await this.stageFiles(files);
      }

      const result = await this.git.commit(message);
      this.invalidateCache();
      
      logger.info('Commit created successfully', { 
        message, 
        hash: result.commit 
      });
      
      return result.commit;
    } catch (error) {
      logger.error('Failed to create commit', { message, files, error });
      throw new ArienError(
        ErrorCode.TOOL_EXECUTION_FAILED,
        `Failed to create commit: ${error instanceof Error ? error.message : String(error)}`,
        { message, files, error }
      );
    }
  }

  public async createBranch(name: string, checkout: boolean = false): Promise<void> {
    try {
      if (checkout) {
        await this.git.checkoutLocalBranch(name);
      } else {
        await this.git.branch([name]);
      }
      
      this.invalidateCache();
      logger.info('Branch created successfully', { name, checkout });
    } catch (error) {
      logger.error('Failed to create branch', { name, error });
      throw new ArienError(
        ErrorCode.TOOL_EXECUTION_FAILED,
        `Failed to create branch: ${error instanceof Error ? error.message : String(error)}`,
        { name, checkout, error }
      );
    }
  }

  public async switchBranch(name: string): Promise<void> {
    try {
      await this.git.checkout(name);
      this.invalidateCache();
      logger.info('Switched to branch successfully', { name });
    } catch (error) {
      logger.error('Failed to switch branch', { name, error });
      throw new ArienError(
        ErrorCode.TOOL_EXECUTION_FAILED,
        `Failed to switch branch: ${error instanceof Error ? error.message : String(error)}`,
        { name, error }
      );
    }
  }

  public async getFileHistory(filePath: string, limit: number = 10): Promise<GitCommit[]> {
    try {
      const log = await this.git.log({ 
        file: filePath, 
        maxCount: limit 
      });
      
      return log.all.map(commit => ({
        hash: commit.hash,
        date: new Date(commit.date),
        message: commit.message,
        author: commit.author_name,
        email: commit.author_email,
        refs: commit.refs
      }));
    } catch (error) {
      logger.error('Failed to get file history', { filePath, error });
      throw new ArienError(
        ErrorCode.TOOL_EXECUTION_FAILED,
        `Failed to get file history: ${error.message}`,
        { filePath, limit, error }
      );
    }
  }

  private async getTrackingInfo(): Promise<{ ahead: number; behind: number }> {
    try {
      const status = await this.git.status();
      return {
        ahead: status.ahead || 0,
        behind: status.behind || 0
      };
    } catch (error) {
      return { ahead: 0, behind: 0 };
    }
  }

  private invalidateCache(): void {
    this.statusCache.clear();
    this.branchCache.clear();
  }

  public setWorkingDirectory(directory: string): void {
    this.workingDirectory = directory;
    this.git = simpleGit(directory);
    this.invalidateCache();
  }

  public getWorkingDirectory(): string {
    return this.workingDirectory;
  }
}
