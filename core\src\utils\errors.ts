// Error type definitions and utilities

export enum ErrorCode {
  // Configuration errors
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',

  // Authentication errors
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  PERMISSION_DENIED = 'PERMISSION_DENIED',

  // API errors
  API_ERROR = 'API_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  RATE_LIMITED = 'RATE_LIMITED',
  QUOTA_EXCEEDED_ERROR = 'QUOTA_EXCEEDED_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  REQUEST_TIMEOUT = 'REQUEST_TIMEOUT',
  REQUEST_ABORTED = 'REQUEST_ABORTED',

  // Provider errors
  MODEL_ERROR = 'MODEL_ERROR',
  PROVIDER_ERROR = 'PROVIDER_ERROR',
  INVALID_PROVIDER = 'INVALID_PROVIDER',
  PROVIDER_NOT_CONFIGURED = 'PROVIDER_NOT_CONFIGURED',
  MODEL_NOT_FOUND = 'MODEL_NOT_FOUND',
  MODEL_NOT_SUPPORTED = 'MODEL_NOT_SUPPORTED',

  // Tool errors
  TOOL_ERROR = 'TOOL_ERROR',
  TOOL_VALIDATION_ERROR = 'TOOL_VALIDATION_ERROR',
  TOOL_EXECUTION_ERROR = 'TOOL_EXECUTION_ERROR',
  TOOL_NOT_FOUND = 'TOOL_NOT_FOUND',
  TOOL_EXECUTION_FAILED = 'TOOL_EXECUTION_FAILED',
  TOOL_EXECUTION_DENIED = 'TOOL_EXECUTION_DENIED',
  TOOL_EXECUTION_TIMEOUT = 'TOOL_EXECUTION_TIMEOUT',
  TOOL_EXECUTION_CANCELLED = 'TOOL_EXECUTION_CANCELLED',

  // File system errors
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  FILE_ACCESS_ERROR = 'FILE_ACCESS_ERROR',
  FILE_ACCESS_DENIED = 'FILE_ACCESS_DENIED',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  DIRECTORY_NOT_FOUND = 'DIRECTORY_NOT_FOUND',
  PATH_TRAVERSAL_DENIED = 'PATH_TRAVERSAL_DENIED',
  INVALID_PATH = 'INVALID_PATH',

  // Security errors
  SECURITY_ERROR = 'SECURITY_ERROR',
  SECURITY_VIOLATION = 'SECURITY_VIOLATION',
  SANDBOX_ERROR = 'SANDBOX_ERROR',
  SANDBOX_VIOLATION = 'SANDBOX_VIOLATION',
  COMMAND_NOT_ALLOWED = 'COMMAND_NOT_ALLOWED',
  RESOURCE_ACCESS_DENIED = 'RESOURCE_ACCESS_DENIED',

  // Session errors
  SESSION_NOT_FOUND = 'SESSION_NOT_FOUND',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  SESSION_INVALID = 'SESSION_INVALID',

  // Network errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  HTTP_ERROR = 'HTTP_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',

  // User errors
  USER_CANCELLED = 'USER_CANCELLED',
  INVALID_INPUT = 'INVALID_INPUT',
  OPERATION_NOT_SUPPORTED = 'OPERATION_NOT_SUPPORTED',

  // System errors
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  MEMORY_ERROR = 'MEMORY_ERROR',
  MEMORY_LIMIT_EXCEEDED = 'MEMORY_LIMIT_EXCEEDED',
  CONTEXT_TOO_LARGE = 'CONTEXT_TOO_LARGE',

  // Extension errors
  EXTENSION_NOT_FOUND = 'EXTENSION_NOT_FOUND',
  EXTENSION_LOAD_FAILED = 'EXTENSION_LOAD_FAILED',
  EXTENSION_INVALID = 'EXTENSION_INVALID',

  // Unknown errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export class ArienError extends Error {
  public readonly code: ErrorCode;
  public readonly details?: any;
  public readonly timestamp: Date;

  constructor(code: ErrorCode, message: string, details?: any) {
    super(message);
    this.name = 'ArienError';
    this.code = code;
    this.details = details;
    this.timestamp = new Date();
    
    // Maintain proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ArienError);
    }
  }

  public toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      details: this.details,
      timestamp: this.timestamp.toISOString(),
      stack: this.stack
    };
  }
}

export class ConfigurationError extends ArienError {
  constructor(message: string, details?: any) {
    super(ErrorCode.CONFIGURATION_ERROR, message, details);
    this.name = 'ConfigurationError';
  }
}

export class AuthenticationError extends ArienError {
  constructor(message: string, details?: any) {
    super(ErrorCode.AUTHENTICATION_ERROR, message, details);
    this.name = 'AuthenticationError';
  }
}

export class ToolError extends ArienError {
  constructor(message: string, details?: any) {
    super(ErrorCode.TOOL_ERROR, message, details);
    this.name = 'ToolError';
  }
}

export class SecurityError extends ArienError {
  constructor(message: string, details?: any) {
    super(ErrorCode.SECURITY_ERROR, message, details);
    this.name = 'SecurityError';
  }
}

export class FileSystemError extends ArienError {
  public readonly path: string;
  public readonly operation: string;

  constructor(
    code: ErrorCode,
    message: string,
    path: string = '',
    operation: string = '',
    details?: any
  ) {
    super(code, message, details);
    this.name = 'FileSystemError';
    this.path = path;
    this.operation = operation;
  }
}

export class ToolExecutionError extends ArienError {
  public readonly toolName: string;

  constructor(toolName: string, message: string, details?: any) {
    super(ErrorCode.TOOL_EXECUTION_FAILED, message, details);
    this.name = 'ToolExecutionError';
    this.toolName = toolName;
  }
}

export class SandboxViolationError extends ArienError {
  public readonly resource: string;
  public readonly action: string;

  constructor(
    resource: string,
    action: string,
    message: string,
    details?: any
  ) {
    super(ErrorCode.SANDBOX_VIOLATION, message, details);
    this.name = 'SandboxViolationError';
    this.resource = resource;
    this.action = action;
  }
}

export class NetworkError extends ArienError {
  public readonly url?: string;
  public readonly statusCode?: number;

  constructor(
    message: string,
    url?: string,
    statusCode?: number,
    details?: any
  ) {
    super(ErrorCode.NETWORK_ERROR, message, details);
    this.name = 'NetworkError';
    this.url = url;
    this.statusCode = statusCode;
  }
}

export function isArienError(error: any): error is ArienError {
  return error instanceof ArienError;
}

export function getErrorMessage(error: unknown): string {
  if (error instanceof ArienError) {
    return error.message;
  }
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unknown error occurred';
}

export function getErrorCode(error: unknown): ErrorCode {
  if (error instanceof ArienError) {
    return error.code;
  }
  return ErrorCode.UNKNOWN_ERROR;
}

export function createErrorFromCode(code: ErrorCode, message: string, details?: any): ArienError {
  switch (code) {
    case ErrorCode.CONFIGURATION_ERROR:
      return new ConfigurationError(message, details);
    case ErrorCode.AUTHENTICATION_ERROR:
      return new AuthenticationError(message, details);
    case ErrorCode.TOOL_ERROR:
    case ErrorCode.TOOL_VALIDATION_ERROR:
    case ErrorCode.TOOL_EXECUTION_ERROR:
      return new ToolError(message, details);
    case ErrorCode.SECURITY_ERROR:
    case ErrorCode.SANDBOX_ERROR:
      return new SecurityError(message, details);
    case ErrorCode.FILE_NOT_FOUND:
    case ErrorCode.FILE_ACCESS_ERROR:
    case ErrorCode.PERMISSION_DENIED:
      return new FileSystemError(code, message, details);
    default:
      return new ArienError(code, message, details);
  }
}

export interface ErrorRecoveryStrategy {
  canRecover: (error: ArienError) => boolean;
  recover: (error: ArienError) => Promise<void>;
  maxRetries?: number;
}

export class ErrorRecoveryManager {
  private strategies: ErrorRecoveryStrategy[] = [];

  public addStrategy(strategy: ErrorRecoveryStrategy): void {
    this.strategies.push(strategy);
  }

  public async tryRecover(error: ArienError): Promise<boolean> {
    for (const strategy of this.strategies) {
      if (strategy.canRecover(error)) {
        try {
          await strategy.recover(error);
          return true;
        } catch (recoveryError) {
          console.warn('Recovery strategy failed:', recoveryError);
        }
      }
    }
    return false;
  }
}

// Error utility functions
export function isRetryableError(error: Error): boolean {
  if (isArienError(error)) {
    const retryableCodes = [
      ErrorCode.NETWORK_ERROR,
      ErrorCode.REQUEST_TIMEOUT,
      ErrorCode.RATE_LIMITED,
      ErrorCode.SERVER_ERROR,
      ErrorCode.TIMEOUT_ERROR
    ];
    return retryableCodes.includes(error.code);
  }

  // Check for common Node.js network errors
  const nodeErrors = ['ECONNRESET', 'ENOTFOUND', 'ECONNREFUSED', 'ETIMEDOUT'];
  return nodeErrors.some(code => (error as any).code === code);
}

export function getErrorSeverity(error: Error): 'low' | 'medium' | 'high' | 'critical' {
  if (isArienError(error)) {
    const criticalCodes = [
      ErrorCode.AUTHENTICATION_FAILED,
      ErrorCode.PERMISSION_DENIED,
      ErrorCode.SANDBOX_VIOLATION,
      ErrorCode.CONFIGURATION_ERROR
    ];

    const highCodes = [
      ErrorCode.TOOL_EXECUTION_FAILED,
      ErrorCode.FILE_ACCESS_DENIED,
      ErrorCode.SESSION_EXPIRED,
      ErrorCode.SECURITY_ERROR
    ];

    const mediumCodes = [
      ErrorCode.VALIDATION_ERROR,
      ErrorCode.TOOL_EXECUTION_DENIED,
      ErrorCode.RATE_LIMITED,
      ErrorCode.MODEL_NOT_FOUND
    ];

    if (criticalCodes.includes(error.code)) return 'critical';
    if (highCodes.includes(error.code)) return 'high';
    if (mediumCodes.includes(error.code)) return 'medium';
    return 'low';
  }

  return 'medium';
}

export function formatErrorForUser(error: Error): string {
  if (isArienError(error)) {
    switch (error.code) {
      case ErrorCode.AUTHENTICATION_FAILED:
        return 'Authentication failed. Please check your API credentials.';
      case ErrorCode.PERMISSION_DENIED:
        return 'Permission denied. You don\'t have access to perform this operation.';
      case ErrorCode.TOOL_EXECUTION_DENIED:
        return 'Tool execution was denied by security policy.';
      case ErrorCode.FILE_NOT_FOUND:
        return 'The requested file was not found.';
      case ErrorCode.RATE_LIMITED:
        return 'Rate limit exceeded. Please wait before making more requests.';
      case ErrorCode.NETWORK_ERROR:
        return 'Network error occurred. Please check your connection.';
      case ErrorCode.SANDBOX_VIOLATION:
        return 'Operation blocked by security sandbox.';
      case ErrorCode.MODEL_NOT_FOUND:
        return 'The specified AI model was not found or is not available.';
      case ErrorCode.PROVIDER_NOT_CONFIGURED:
        return 'AI provider is not properly configured. Please check your settings.';
      default:
        return error.message;
    }
  }

  return error.message;
}

export function wrapError(
  error: Error,
  code: ErrorCode,
  message?: string,
  details?: any
): ArienError {
  if (isArienError(error)) {
    return error;
  }

  return new ArienError(
    code,
    message || error.message,
    { ...details, originalError: error.message }
  );
}
