// Tool registration and management system
import { <PERSON><PERSON><PERSON>, <PERSON>l<PERSON><PERSON>x<PERSON>, ToolResult, ToolExecutionError, toolStatsCollector } from './tools.js';
import { logger } from '../core/logger.js';

export interface ToolApprovalRequest {
  toolName: string;
  parameters: any;
  description: string;
  riskLevel: 'low' | 'medium' | 'high';
  context: ToolContext;
}

export interface ToolApprovalResponse {
  approved: boolean;
  reason?: string;
  rememberChoice?: boolean;
}

export type ApprovalHandler = (request: ToolApprovalRequest) => Promise<ToolApprovalResponse>;

export class ToolRegistry {
  private tools = new Map<string, BaseTool>();
  private approvalHandler?: ApprovalHandler;
  private approvedOperations = new Set<string>();

  public registerTool(tool: BaseTool): void {
    this.tools.set(tool.definition.name, tool);
    logger.debug('Tool registered', { toolName: tool.definition.name });
  }

  public unregisterTool(toolName: string): boolean {
    const removed = this.tools.delete(toolName);
    if (removed) {
      logger.debug('Tool unregistered', { toolName });
    }
    return removed;
  }

  public getTool(toolName: string): BaseTool | undefined {
    return this.tools.get(toolName);
  }

  public getAvailableTools(): string[] {
    return Array.from(this.tools.keys());
  }

  public getToolDefinitions(): Record<string, any> {
    const toolsRecord: Record<string, any> = {};

    for (const tool of this.tools.values()) {
      toolsRecord[tool.definition.name] = {
        description: tool.definition.description,
        parameters: tool.definition.parameters,
        execute: async (args: any, options?: { abortSignal?: AbortSignal }) => {
          // Create a basic tool context for execution
          const context: ToolContext = {
            workingDirectory: process.cwd(),
            userId: 'system',
            sessionId: 'default',
            approvalLevel: 'default',
            allowedCommands: [],
            sandboxEnabled: false
          };

          const result = await tool.execute(args, context);
          return result.content;
        }
      };
    }

    return toolsRecord;
  }

  public setApprovalHandler(handler: ApprovalHandler): void {
    this.approvalHandler = handler;
  }

  public addApprovedOperation(operationKey: string): void {
    this.approvedOperations.add(operationKey);
  }

  public isOperationApproved(operationKey: string): boolean {
    return this.approvedOperations.has(operationKey);
  }

  private generateOperationKey(toolName: string, params: any): string {
    // Create a simple hash of the tool name and parameters for approval caching
    const paramStr = JSON.stringify(params, Object.keys(params).sort());
    return `${toolName}:${Buffer.from(paramStr).toString('base64').slice(0, 16)}`;
  }

  public async executeTool(
    toolName: string,
    parameters: any,
    context: ToolContext
  ): Promise<ToolResult> {
    const startTime = Date.now();
    let success = false;
    let error: string | undefined;

    try {
      const tool = this.tools.get(toolName);
      if (!tool) {
        throw new ToolExecutionError(toolName, `Tool '${toolName}' not found`);
      }

      logger.debug('Tool execution started', { toolName, parameters, context: { ...context, userId: '[REDACTED]' } });

      // Validate parameters
      let validatedParams;
      try {
        validatedParams = tool.definition.parameters.parse(parameters);
      } catch (validationError) {
        throw new ToolExecutionError(toolName, `Parameter validation failed: ${validationError}`);
      }

      // Check if approval is required
      if (tool.definition.requiresApproval || tool.definition.riskLevel === 'high') {
        const operationKey = this.generateOperationKey(toolName, validatedParams);
        
        if (!this.isOperationApproved(operationKey) && context.approvalLevel !== 'yolo') {
          if (!this.approvalHandler) {
            throw new ToolExecutionError(toolName, 'Tool requires approval but no approval handler is set');
          }

          const approvalRequest: ToolApprovalRequest = {
            toolName,
            parameters: validatedParams,
            description: tool.definition.description,
            riskLevel: tool.definition.riskLevel || 'medium',
            context
          };

          const approvalResponse = await this.approvalHandler(approvalRequest);
          
          if (!approvalResponse.approved) {
            throw new ToolExecutionError(toolName, `Tool execution denied: ${approvalResponse.reason || 'User denied approval'}`);
          }

          if (approvalResponse.rememberChoice) {
            this.addApprovedOperation(operationKey);
          }
        }
      }

      // Execute the tool
      const result = await tool.execute(validatedParams, context);
      success = result.success;
      
      if (!result.success) {
        error = result.error;
      }

      logger.debug('Tool execution completed', { 
        toolName, 
        success: result.success, 
        executionTime: Date.now() - startTime 
      });

      return result;
    } catch (err) {
      success = false;
      error = err instanceof Error ? err.message : 'Unknown error';
      
      logger.error('Tool execution failed', { 
        toolName, 
        error: err, 
        executionTime: Date.now() - startTime 
      });

      if (err instanceof ToolExecutionError) {
        throw err;
      }
      
      throw new ToolExecutionError(toolName, `Unexpected error: ${err}`);
    } finally {
      // Record execution statistics
      toolStatsCollector.recordExecution({
        toolName,
        executionTime: Date.now() - startTime,
        success,
        error,
        timestamp: new Date(),
        userId: context.userId,
        sessionId: context.sessionId
      });
    }
  }

  public async executeMultipleTools(
    toolCalls: Array<{ toolName: string; parameters: any }>,
    context: ToolContext
  ): Promise<ToolResult[]> {
    const results: ToolResult[] = [];
    
    for (const toolCall of toolCalls) {
      try {
        const result = await this.executeTool(toolCall.toolName, toolCall.parameters, context);
        results.push(result);
        
        // Stop execution if a tool fails and it's marked as critical
        if (!result.success) {
          logger.warn('Tool execution failed in batch', { 
            toolName: toolCall.toolName, 
            error: result.error 
          });
          // Continue with other tools unless it's a critical failure
        }
      } catch (error) {
        logger.error('Tool execution error in batch', { 
          toolName: toolCall.toolName, 
          error 
        });
        
        results.push({
          success: false,
          content: '',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
    
    return results;
  }

  public getToolStats(toolName?: string) {
    return {
      executions: toolStatsCollector.getStats(toolName),
      successRate: toolStatsCollector.getSuccessRate(toolName),
      averageExecutionTime: toolStatsCollector.getAverageExecutionTime(toolName)
    };
  }

  public clearStats(): void {
    toolStatsCollector.clear();
  }

  public validateToolCall(toolName: string, parameters: any): { valid: boolean; errors: string[] } {
    const tool = this.tools.get(toolName);
    if (!tool) {
      return { valid: false, errors: [`Tool '${toolName}' not found`] };
    }

    try {
      tool.definition.parameters.parse(parameters);
      return { valid: true, errors: [] };
    } catch (error) {
      return { 
        valid: false, 
        errors: [error instanceof Error ? error.message : 'Validation failed'] 
      };
    }
  }
}

// Global tool registry instance
export const globalToolRegistry = new ToolRegistry();
