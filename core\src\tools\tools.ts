// Core tool definitions and interfaces
import { z } from 'zod';

export interface ToolResult {
  success: boolean;
  content: string;
  error?: string;
  metadata?: Record<string, any>;
  executionTime?: number;
  data?: any;
}

export interface ToolContext {
  workingDirectory: string;
  userId: string;
  sessionId: string;
  approvalLevel: 'default' | 'auto-edit' | 'yolo';
  allowedCommands: string[];
  sandboxEnabled: boolean;
  nonInteractive?: boolean;
}

export interface ToolDefinition {
  name: string;
  description: string;
  parameters: z.ZodSchema;
  requiresApproval?: boolean;
  riskLevel?: 'low' | 'medium' | 'high';
  category?: 'file' | 'shell' | 'web' | 'memory' | 'system';
}

export abstract class BaseTool {
  public readonly definition: ToolDefinition;

  constructor(definition: ToolDefinition) {
    this.definition = definition;
  }

  abstract execute(params: any, context: ToolContext): Promise<ToolResult>;

  protected validateParams(params: any): any {
    return this.definition.parameters.parse(params);
  }

  protected requiresUserApproval(context: ToolContext): boolean {
    if (context.approvalLevel === 'yolo') {
      return false;
    }
    
    if (context.approvalLevel === 'auto-edit' && this.definition.category === 'file') {
      return false;
    }
    
    return this.definition.requiresApproval || this.definition.riskLevel === 'high';
  }

  protected createSuccessResult(content: string, metadata?: Record<string, any>): ToolResult {
    return {
      success: true,
      content,
      metadata
    };
  }

  protected createErrorResult(error: string, metadata?: Record<string, any>): ToolResult {
    return {
      success: false,
      content: '',
      error,
      metadata
    };
  }
}

// Common parameter schemas
export const FilePathSchema = z.object({
  path: z.string().describe('File or directory path')
});

export const FileContentSchema = z.object({
  path: z.string().describe('File path'),
  content: z.string().describe('File content')
});

export const ShellCommandSchema = z.object({
  command: z.string().describe('Shell command to execute'),
  workingDirectory: z.string().optional().describe('Working directory for command execution')
});

export const SearchSchema = z.object({
  query: z.string().describe('Search query'),
  path: z.string().optional().describe('Path to search in (defaults to current directory)'),
  includeHidden: z.boolean().optional().default(false).describe('Include hidden files and directories')
});

export const WebSearchSchema = z.object({
  query: z.string().describe('Web search query'),
  maxResults: z.number().optional().default(5).describe('Maximum number of results to return')
});

export const WebFetchSchema = z.object({
  url: z.string().url().describe('URL to fetch content from'),
  timeout: z.number().optional().default(10000).describe('Request timeout in milliseconds')
});

// Tool execution errors
export class ToolExecutionError extends Error {
  constructor(
    public readonly toolName: string,
    message: string,
    public readonly originalError?: Error
  ) {
    super(`Tool '${toolName}' failed: ${message}`);
    this.name = 'ToolExecutionError';
  }
}

export class ToolValidationError extends Error {
  constructor(
    public readonly toolName: string,
    message: string,
    public readonly validationErrors?: any[]
  ) {
    super(`Tool '${toolName}' validation failed: ${message}`);
    this.name = 'ToolValidationError';
  }
}

export class ToolPermissionError extends Error {
  constructor(
    public readonly toolName: string,
    message: string
  ) {
    super(`Tool '${toolName}' permission denied: ${message}`);
    this.name = 'ToolPermissionError';
  }
}

// Tool execution statistics
export interface ToolExecutionStats {
  toolName: string;
  executionTime: number;
  success: boolean;
  error?: string;
  timestamp: Date;
  userId: string;
  sessionId: string;
}

export class ToolStatsCollector {
  private stats: ToolExecutionStats[] = [];

  public recordExecution(stats: ToolExecutionStats): void {
    this.stats.push(stats);
    
    // Keep only last 1000 executions
    if (this.stats.length > 1000) {
      this.stats = this.stats.slice(-1000);
    }
  }

  public getStats(toolName?: string): ToolExecutionStats[] {
    if (toolName) {
      return this.stats.filter(stat => stat.toolName === toolName);
    }
    return [...this.stats];
  }

  public getSuccessRate(toolName?: string): number {
    const relevantStats = this.getStats(toolName);
    if (relevantStats.length === 0) return 0;
    
    const successCount = relevantStats.filter(stat => stat.success).length;
    return successCount / relevantStats.length;
  }

  public getAverageExecutionTime(toolName?: string): number {
    const relevantStats = this.getStats(toolName);
    if (relevantStats.length === 0) return 0;
    
    const totalTime = relevantStats.reduce((sum, stat) => sum + stat.executionTime, 0);
    return totalTime / relevantStats.length;
  }

  public clear(): void {
    this.stats = [];
  }
}

// Global stats collector
export const toolStatsCollector = new ToolStatsCollector();
