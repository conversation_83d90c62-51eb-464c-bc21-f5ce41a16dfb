// Telemetry exports
export * from './types.js';
export * from './constants.js';

// Metrics exports (avoiding conflicts)
export {
  MetricsCollector,
  getMetricsCollector,
  startMetricsCollection,
  stopMetricsCollection,
  recordEvent as recordEventSync,
  recordMetric as recordMetricSync,
  incrementCounter,
  decrementCounter,
  recordGauge,
  recordHistogram,
  recordTimer
} from './metrics.js';

// Logger exports (avoiding conflicts)
export {
  TelemetryLogger,
  initTelemetryLogger,
  shutdownTelemetry as shutdownTelemetryLogger
} from './loggers.js';

// SDK exports (primary telemetry interface)
export {
  TelemetrySDK,
  initTelemetry,
  shutdownTelemetry,
  recordEvent,
  recordMetric,
  recordError,
  recordCommand,
  recordTool,
  recordProviderSwitch,
  recordApproval,
  recordFileAccess,
  recordNetworkRequest,
  telemetry
} from './sdk.js';
export interface TelemetryEvent {
  name: string;
  properties: Record<string, any>;
  timestamp: Date;
  userId: string;
  sessionId: string;
}

export interface TelemetryConfig {
  enabled: boolean;
  endpoint?: string;
  apiKey?: string;
  batchSize: number;
  flushInterval: number;
}

export class TelemetryManager {
  private config: TelemetryConfig;
  private events: TelemetryEvent[] = [];
  private flushTimer?: NodeJS.Timeout;

  constructor(config: TelemetryConfig) {
    this.config = config;
    
    if (config.enabled && config.flushInterval > 0) {
      this.startFlushTimer();
    }
  }

  public track(name: string, properties: Record<string, any> = {}, userId: string, sessionId: string): void {
    if (!this.config.enabled) return;

    const event: TelemetryEvent = {
      name,
      properties,
      timestamp: new Date(),
      userId,
      sessionId
    };

    this.events.push(event);

    if (this.events.length >= this.config.batchSize) {
      this.flush();
    }
  }

  public async flush(): Promise<void> {
    if (!this.config.enabled || this.events.length === 0) return;

    const eventsToSend = [...this.events];
    this.events = [];

    try {
      if (this.config.endpoint) {
        await this.sendEvents(eventsToSend);
      }
    } catch (error) {
      // Silently fail telemetry to not disrupt user experience
      console.debug('Telemetry flush failed:', error);
    }
  }

  private async sendEvents(events: TelemetryEvent[]): Promise<void> {
    if (!this.config.endpoint) return;

    const response = await fetch(this.config.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
      },
      body: JSON.stringify({ events })
    });

    if (!response.ok) {
      throw new Error(`Telemetry request failed: ${response.status}`);
    }
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);
  }

  public updateConfig(newConfig: Partial<TelemetryConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = undefined;
    }
    
    if (this.config.enabled && this.config.flushInterval > 0) {
      this.startFlushTimer();
    }
  }

  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flush();
  }
}

// Default telemetry configuration
export const defaultTelemetryConfig: TelemetryConfig = {
  enabled: false, // Disabled by default for privacy
  batchSize: 10,
  flushInterval: 30000 // 30 seconds
};

// Global telemetry manager
export const telemetryManager = new TelemetryManager(defaultTelemetryConfig);
