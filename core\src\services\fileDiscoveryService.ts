// File discovery and indexing service
import * as fs from 'fs-extra';
import * as path from 'path';
import { glob } from 'fast-glob';
import { minimatch } from 'minimatch';
import { logger } from '../core/logger.js';
import { ArienError, ErrorCode } from '../utils/errors.js';
import { LruCache } from '../utils/LruCache.js';

export interface FileInfo {
  path: string;
  name: string;
  extension: string;
  size: number;
  mtime: Date;
  isDirectory: boolean;
  relativePath: string;
  language?: string;
  encoding?: string;
}

export interface DirectoryInfo {
  path: string;
  name: string;
  fileCount: number;
  directoryCount: number;
  totalSize: number;
  files: FileInfo[];
  subdirectories: DirectoryInfo[];
}

export interface SearchOptions {
  pattern?: string;
  extensions?: string[];
  maxDepth?: number;
  includeHidden?: boolean;
  followSymlinks?: boolean;
  maxSize?: number;
  modifiedAfter?: Date;
  modifiedBefore?: Date;
  excludePatterns?: string[];
  includeContent?: boolean;
}

export interface SearchResult {
  files: FileInfo[];
  totalCount: number;
  searchTime: number;
  query: SearchOptions;
}

export class FileDiscoveryService {
  private cache = new LruCache<string, FileInfo[]>(100);
  private indexCache = new LruCache<string, DirectoryInfo>(50);
  private readonly DEFAULT_EXCLUDE_PATTERNS = [
    'node_modules/**',
    '.git/**',
    '.svn/**',
    '.hg/**',
    'dist/**',
    'build/**',
    'coverage/**',
    '*.log',
    '.DS_Store',
    'Thumbs.db'
  ];

  public async discoverFiles(
    rootPath: string,
    options: SearchOptions = {}
  ): Promise<SearchResult> {
    const startTime = Date.now();
    
    try {
      const normalizedPath = path.resolve(rootPath);
      
      // Check if path exists
      if (!await fs.pathExists(normalizedPath)) {
        throw new ArienError(
          ErrorCode.DIRECTORY_NOT_FOUND,
          `Directory not found: ${rootPath}`
        );
      }

      const cacheKey = this.generateCacheKey(normalizedPath, options);
      const cached = this.cache.get(cacheKey);
      
      if (cached) {
        logger.debug('Using cached file discovery results', { 
          path: rootPath, 
          fileCount: cached.length 
        });
        
        return {
          files: cached,
          totalCount: cached.length,
          searchTime: Date.now() - startTime,
          query: options
        };
      }

      const files = await this.searchFiles(normalizedPath, options);
      
      // Cache the results
      this.cache.set(cacheKey, files);
      
      const searchTime = Date.now() - startTime;
      
      logger.info('File discovery completed', {
        path: rootPath,
        fileCount: files.length,
        searchTime
      });

      return {
        files,
        totalCount: files.length,
        searchTime,
        query: options
      };
    } catch (error) {
      logger.error('File discovery failed', { path: rootPath, error });
      throw new ArienError(
        ErrorCode.FILE_ACCESS_ERROR,
        `File discovery failed: ${error instanceof Error ? error.message : String(error)}`,
        { path: rootPath, options, error }
      );
    }
  }

  public async indexDirectory(
    rootPath: string,
    maxDepth: number = 3
  ): Promise<DirectoryInfo> {
    try {
      const normalizedPath = path.resolve(rootPath);
      const cacheKey = `${normalizedPath}:${maxDepth}`;
      const cached = this.indexCache.get(cacheKey);
      
      if (cached) {
        return cached;
      }

      const directoryInfo = await this.buildDirectoryIndex(normalizedPath, maxDepth);
      this.indexCache.set(cacheKey, directoryInfo);
      
      return directoryInfo;
    } catch (error) {
      logger.error('Directory indexing failed', { path: rootPath, error });
      throw new ArienError(
        ErrorCode.FILE_ACCESS_ERROR,
        `Directory indexing failed: ${error instanceof Error ? error.message : String(error)}`,
        { path: rootPath, maxDepth, error }
      );
    }
  }

  public async findFilesByPattern(
    rootPath: string,
    pattern: string,
    options: Partial<SearchOptions> = {}
  ): Promise<FileInfo[]> {
    const searchOptions: SearchOptions = {
      pattern,
      maxDepth: 10,
      includeHidden: false,
      followSymlinks: false,
      ...options
    };

    const result = await this.discoverFiles(rootPath, searchOptions);
    return result.files;
  }

  public async findFilesByExtension(
    rootPath: string,
    extensions: string[],
    options: Partial<SearchOptions> = {}
  ): Promise<FileInfo[]> {
    const searchOptions: SearchOptions = {
      extensions,
      maxDepth: 10,
      includeHidden: false,
      ...options
    };

    const result = await this.discoverFiles(rootPath, searchOptions);
    return result.files;
  }

  public async getFileInfo(filePath: string): Promise<FileInfo> {
    try {
      const normalizedPath = path.resolve(filePath);
      const stats = await fs.stat(normalizedPath);
      
      return this.createFileInfo(normalizedPath, stats);
    } catch (error) {
      throw new ArienError(
        ErrorCode.FILE_NOT_FOUND,
        `File not found: ${filePath}`,
        { path: filePath, error }
      );
    }
  }

  public async getDirectoryStats(dirPath: string): Promise<{
    fileCount: number;
    directoryCount: number;
    totalSize: number;
  }> {
    try {
      const files = await this.discoverFiles(dirPath, { maxDepth: 1 });
      
      let fileCount = 0;
      let directoryCount = 0;
      let totalSize = 0;

      for (const file of files.files) {
        if (file.isDirectory) {
          directoryCount++;
        } else {
          fileCount++;
          totalSize += file.size;
        }
      }

      return { fileCount, directoryCount, totalSize };
    } catch (error) {
      throw new ArienError(
        ErrorCode.DIRECTORY_NOT_FOUND,
        `Directory stats failed: ${error instanceof Error ? error.message : String(error)}`,
        { path: dirPath, error }
      );
    }
  }

  private async searchFiles(
    rootPath: string,
    options: SearchOptions
  ): Promise<FileInfo[]> {
    const {
      pattern = '**/*',
      extensions = [],
      maxDepth = 10,
      includeHidden = false,
      followSymlinks = false,
      maxSize,
      modifiedAfter,
      modifiedBefore,
      excludePatterns = []
    } = options;

    // Build glob patterns
    let globPattern = pattern;
    if (extensions.length > 0) {
      const extPattern = extensions.length === 1 
        ? `*.${extensions[0]}` 
        : `*.{${extensions.join(',')}}`;
      globPattern = pattern.includes('*') ? pattern : `${pattern}/${extPattern}`;
    }

    // Combine default and custom exclude patterns
    const allExcludePatterns = [
      ...this.DEFAULT_EXCLUDE_PATTERNS,
      ...excludePatterns
    ];

    const globOptions = {
      cwd: rootPath,
      absolute: true,
      dot: includeHidden,
      followSymbolicLinks: followSymlinks,
      deep: maxDepth,
      ignore: allExcludePatterns,
      stats: true
    };

    const matches = await glob(globPattern, globOptions);
    const files: FileInfo[] = [];

    for (const match of matches) {
      try {
        const stats = await fs.stat(match);
        
        // Apply size filter
        if (maxSize && stats.size > maxSize) {
          continue;
        }

        // Apply date filters
        if (modifiedAfter && stats.mtime < modifiedAfter) {
          continue;
        }
        
        if (modifiedBefore && stats.mtime > modifiedBefore) {
          continue;
        }

        const fileInfo = this.createFileInfo(match, stats, rootPath);
        files.push(fileInfo);
      } catch (error) {
        logger.debug('Failed to get file stats', { path: match, error });
      }
    }

    return files;
  }

  private async buildDirectoryIndex(
    dirPath: string,
    maxDepth: number,
    currentDepth: number = 0
  ): Promise<DirectoryInfo> {
    const stats = await fs.stat(dirPath);
    const entries = await fs.readdir(dirPath);
    
    const files: FileInfo[] = [];
    const subdirectories: DirectoryInfo[] = [];
    let totalSize = 0;

    for (const entry of entries) {
      const entryPath = path.join(dirPath, entry);
      
      try {
        const entryStats = await fs.stat(entryPath);
        
        if (entryStats.isDirectory()) {
          if (currentDepth < maxDepth) {
            const subDirInfo = await this.buildDirectoryIndex(
              entryPath, 
              maxDepth, 
              currentDepth + 1
            );
            subdirectories.push(subDirInfo);
            totalSize += subDirInfo.totalSize;
          }
        } else {
          const fileInfo = this.createFileInfo(entryPath, entryStats, dirPath);
          files.push(fileInfo);
          totalSize += fileInfo.size;
        }
      } catch (error) {
        logger.debug('Failed to process directory entry', { 
          path: entryPath, 
          error 
        });
      }
    }

    return {
      path: dirPath,
      name: path.basename(dirPath),
      fileCount: files.length,
      directoryCount: subdirectories.length,
      totalSize,
      files,
      subdirectories
    };
  }

  private createFileInfo(
    filePath: string, 
    stats: fs.Stats, 
    rootPath?: string
  ): FileInfo {
    const extension = path.extname(filePath).slice(1);
    const relativePath = rootPath 
      ? path.relative(rootPath, filePath) 
      : filePath;

    return {
      path: filePath,
      name: path.basename(filePath),
      extension,
      size: stats.size,
      mtime: stats.mtime,
      isDirectory: stats.isDirectory(),
      relativePath,
      language: this.detectLanguage(extension),
      encoding: this.detectEncoding(extension)
    };
  }

  private detectLanguage(extension: string): string | undefined {
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'ts': 'typescript',
      'jsx': 'javascript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'sh': 'shell',
      'bash': 'shell',
      'zsh': 'shell',
      'fish': 'shell',
      'ps1': 'powershell',
      'sql': 'sql',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'xml': 'xml',
      'json': 'json',
      'yaml': 'yaml',
      'yml': 'yaml',
      'toml': 'toml',
      'ini': 'ini',
      'cfg': 'ini',
      'conf': 'ini',
      'md': 'markdown',
      'markdown': 'markdown',
      'txt': 'text',
      'log': 'text'
    };

    return languageMap[extension.toLowerCase()];
  }

  private detectEncoding(extension: string): string {
    const binaryExtensions = [
      'exe', 'dll', 'so', 'dylib', 'bin', 'dat',
      'jpg', 'jpeg', 'png', 'gif', 'bmp', 'ico',
      'mp3', 'mp4', 'avi', 'mov', 'wav', 'flac',
      'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
      'zip', 'tar', 'gz', 'rar', '7z', 'bz2'
    ];

    return binaryExtensions.includes(extension.toLowerCase()) ? 'binary' : 'utf-8';
  }

  private generateCacheKey(path: string, options: SearchOptions): string {
    return `${path}:${JSON.stringify(options)}`;
  }

  public clearCache(): void {
    this.cache.clear();
    this.indexCache.clear();
    logger.debug('File discovery cache cleared');
  }
}
