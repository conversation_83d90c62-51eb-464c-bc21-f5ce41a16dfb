// File reading tool with intelligent content handling
import { readFile, stat } from 'fs/promises';
import { join, resolve, isAbsolute } from 'path';
import { z } from 'zod';
import { BaseTool, ToolContext, ToolResult, FilePathSchema } from './tools.js';
import { logger } from '../core/logger.js';

const ReadFileSchema = z.object({
  path: z.string().describe('Path to the file to read'),
  encoding: z.enum(['utf8', 'base64', 'hex']).optional().default('utf8').describe('File encoding'),
  maxSize: z.number().optional().default(1024 * 1024).describe('Maximum file size in bytes (default: 1MB)')
});

export class ReadFileTool extends BaseTool {
  constructor() {
    super({
      name: 'read_file',
      description: 'Read the contents of a file',
      parameters: ReadFileSchema,
      requiresApproval: false,
      riskLevel: 'low',
      category: 'file'
    });
  }

  async execute(params: z.infer<typeof ReadFileSchema>, context: ToolContext): Promise<ToolResult> {
    try {
      const validatedParams = this.validateParams(params);
      const { path, encoding, maxSize } = validatedParams;

      // Resolve the file path
      const filePath = isAbsolute(path) ? path : resolve(context.workingDirectory, path);
      
      logger.debug('Reading file', { filePath, encoding, maxSize });

      // Check if file exists and get stats
      let fileStats;
      try {
        fileStats = await stat(filePath);
      } catch (error) {
        return this.createErrorResult(`File not found: ${path}`);
      }

      // Check if it's a file (not a directory)
      if (!fileStats.isFile()) {
        return this.createErrorResult(`Path is not a file: ${path}`);
      }

      // Check file size
      if (fileStats.size > maxSize) {
        return this.createErrorResult(
          `File too large: ${fileStats.size} bytes (max: ${maxSize} bytes). Use a larger maxSize parameter if needed.`
        );
      }

      // Read the file
      const content = await readFile(filePath, encoding);
      
      // Detect if file is binary (for utf8 encoding)
      let isBinary = false;
      if (encoding === 'utf8') {
        // Simple binary detection: check for null bytes
        isBinary = content.includes('\0');
      }

      const metadata = {
        path: filePath,
        size: fileStats.size,
        encoding,
        isBinary,
        lastModified: fileStats.mtime.toISOString(),
        created: fileStats.birthtime.toISOString()
      };

      if (isBinary && encoding === 'utf8') {
        return this.createSuccessResult(
          `File appears to be binary. Use encoding 'base64' or 'hex' to read binary files.`,
          metadata
        );
      }

      logger.debug('File read successfully', { filePath, size: fileStats.size });

      return this.createSuccessResult(content.toString(), metadata);
    } catch (error) {
      logger.error('Failed to read file', { error, params });
      return this.createErrorResult(`Failed to read file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

// Batch file reading tool
const ReadMultipleFilesSchema = z.object({
  paths: z.array(z.string()).describe('Array of file paths to read'),
  encoding: z.enum(['utf8', 'base64', 'hex']).optional().default('utf8').describe('File encoding'),
  maxSizePerFile: z.number().optional().default(1024 * 1024).describe('Maximum size per file in bytes'),
  maxTotalSize: z.number().optional().default(10 * 1024 * 1024).describe('Maximum total size in bytes'),
  continueOnError: z.boolean().optional().default(true).describe('Continue reading other files if one fails')
});

export class ReadMultipleFilesTool extends BaseTool {
  constructor() {
    super({
      name: 'read_multiple_files',
      description: 'Read the contents of multiple files at once',
      parameters: ReadMultipleFilesSchema,
      requiresApproval: false,
      riskLevel: 'low',
      category: 'file'
    });
  }

  async execute(params: z.infer<typeof ReadMultipleFilesSchema>, context: ToolContext): Promise<ToolResult> {
    try {
      const validatedParams = this.validateParams(params);
      const { paths, encoding, maxSizePerFile, maxTotalSize, continueOnError } = validatedParams;

      logger.debug('Reading multiple files', { pathCount: paths.length, encoding });

      const results: Array<{
        path: string;
        success: boolean;
        content?: string;
        error?: string;
        metadata?: any;
      }> = [];

      let totalSize = 0;
      const readFileTool = new ReadFileTool();

      for (const path of paths) {
        try {
          const result = await readFileTool.execute(
            { path, encoding, maxSize: maxSizePerFile },
            context
          );

          if (result.success) {
            const fileSize = result.metadata?.size || 0;
            totalSize += fileSize;

            if (totalSize > maxTotalSize) {
              results.push({
                path,
                success: false,
                error: `Total size limit exceeded (${maxTotalSize} bytes)`
              });
              break;
            }

            results.push({
              path,
              success: true,
              content: result.content,
              metadata: result.metadata
            });
          } else {
            results.push({
              path,
              success: false,
              error: result.error
            });

            if (!continueOnError) {
              break;
            }
          }
        } catch (error) {
          results.push({
            path,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });

          if (!continueOnError) {
            break;
          }
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;

      const summary = `Read ${successCount} files successfully, ${failureCount} failed. Total size: ${totalSize} bytes.`;
      
      const content = JSON.stringify({
        summary,
        results,
        totalSize,
        successCount,
        failureCount
      }, null, 2);

      logger.debug('Multiple files read completed', { successCount, failureCount, totalSize });

      return this.createSuccessResult(content, {
        totalFiles: paths.length,
        successCount,
        failureCount,
        totalSize
      });
    } catch (error) {
      logger.error('Failed to read multiple files', { error, params });
      return this.createErrorResult(`Failed to read multiple files: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
