// File editing tool with intelligent diff application
import { z } from 'zod';
import { promises as fs } from 'fs';
import * as path from 'path';
import { BaseTool, ToolResult, ToolContext, ToolExecutionError } from './tools.js';
import { logger } from '../core/logger.js';

export const EditFileSchema = z.object({
  path: z.string().describe('Path to the file to edit'),
  operation: z.enum(['replace', 'insert', 'delete', 'append', 'prepend']).describe('Type of edit operation'),
  content: z.string().optional().describe('Content to insert or replace with'),
  lineNumber: z.number().optional().describe('Line number for insert/delete operations (1-based)'),
  startLine: z.number().optional().describe('Start line for replace operations (1-based)'),
  endLine: z.number().optional().describe('End line for replace operations (1-based)'),
  searchText: z.string().optional().describe('Text to search for and replace'),
  replaceText: z.string().optional().describe('Text to replace the search text with'),
  createBackup: z.boolean().optional().default(true).describe('Create backup before editing')
});

export interface EditOperation {
  type: 'replace' | 'insert' | 'delete' | 'append' | 'prepend';
  content?: string;
  lineNumber?: number;
  startLine?: number;
  endLine?: number;
  searchText?: string;
  replaceText?: string;
}

export interface EditResult {
  originalContent: string;
  modifiedContent: string;
  linesChanged: number;
  backupPath?: string;
  diff: string;
}

export class EditFileTool extends BaseTool {
  constructor() {
    super({
      name: 'edit-file',
      description: 'Edit files with various operations like replace, insert, delete, append, or prepend',
      parameters: EditFileSchema,
      requiresApproval: true,
      riskLevel: 'medium',
      category: 'file'
    });
  }

  public async execute(params: any, context: ToolContext): Promise<ToolResult> {
    try {
      const validatedParams = this.validateParams(params);
      const { path: filePath, operation, createBackup } = validatedParams;

      // Resolve absolute path
      const absolutePath = path.resolve(context.workingDirectory, filePath);
      
      // Security check - ensure path is within working directory
      if (!absolutePath.startsWith(context.workingDirectory)) {
        throw new ToolExecutionError(
          this.definition.name,
          'File path is outside the allowed working directory'
        );
      }

      // Check if file exists
      const fileExists = await this.fileExists(absolutePath);
      if (!fileExists && operation !== 'append' && operation !== 'prepend') {
        throw new ToolExecutionError(
          this.definition.name,
          `File does not exist: ${filePath}`
        );
      }

      // Read original content
      let originalContent = '';
      if (fileExists) {
        originalContent = await fs.readFile(absolutePath, 'utf-8');
      }

      // Create backup if requested
      let backupPath: string | undefined;
      if (createBackup && fileExists) {
        backupPath = await this.createBackup(absolutePath, originalContent);
      }

      // Perform the edit operation
      const editResult = await this.performEdit(originalContent, validatedParams);

      // Write the modified content
      await fs.writeFile(absolutePath, editResult.modifiedContent, 'utf-8');

      // Generate diff
      const diff = this.generateDiff(originalContent, editResult.modifiedContent);

      logger.info('File edited successfully', {
        filePath,
        operation,
        linesChanged: editResult.linesChanged,
        backupCreated: !!backupPath
      });

      const result: EditResult = {
        originalContent,
        modifiedContent: editResult.modifiedContent,
        linesChanged: editResult.linesChanged,
        backupPath,
        diff
      };

      return this.createSuccessResult(
        `File edited successfully. ${editResult.linesChanged} lines changed.`,
        result
      );

    } catch (error) {
      logger.error('File edit failed', {
        error: error instanceof Error ? error.message : String(error),
        params: this.sanitizeParams(params)
      });

      if (error instanceof ToolExecutionError) {
        throw error;
      }

      throw new ToolExecutionError(
        this.definition.name,
        error instanceof Error ? error.message : String(error),
        error instanceof Error ? error : undefined
      );
    }
  }

  private async performEdit(originalContent: string, params: any): Promise<EditResult> {
    const lines = originalContent.split('\n');
    let modifiedLines = [...lines];
    let linesChanged = 0;

    switch (params.operation) {
      case 'replace':
        if (params.searchText && params.replaceText !== undefined) {
          // Text-based replace
          const modifiedContent = originalContent.replace(
            new RegExp(this.escapeRegex(params.searchText), 'g'),
            params.replaceText
          );
          const originalLineCount = lines.length;
          const modifiedLineCount = modifiedContent.split('\n').length;
          linesChanged = Math.abs(modifiedLineCount - originalLineCount);
          
          const diff = this.generateDiff(originalContent, modifiedContent);
          return {
            originalContent,
            modifiedContent,
            linesChanged,
            diff
          };
        } else if (params.startLine && params.endLine && params.content !== undefined) {
          // Line-based replace
          const startIdx = params.startLine - 1;
          const endIdx = params.endLine - 1;
          
          if (startIdx < 0 || endIdx >= lines.length || startIdx > endIdx) {
            throw new Error('Invalid line range for replace operation');
          }
          
          const newLines = params.content.split('\n');
          modifiedLines.splice(startIdx, endIdx - startIdx + 1, ...newLines);
          linesChanged = Math.abs(newLines.length - (endIdx - startIdx + 1));
        } else {
          throw new Error('Replace operation requires either searchText/replaceText or startLine/endLine/content');
        }
        break;

      case 'insert':
        if (!params.lineNumber || params.content === undefined) {
          throw new Error('Insert operation requires lineNumber and content');
        }
        
        const insertIdx = params.lineNumber - 1;
        if (insertIdx < 0 || insertIdx > lines.length) {
          throw new Error('Invalid line number for insert operation');
        }
        
        const insertLines = params.content.split('\n');
        modifiedLines.splice(insertIdx, 0, ...insertLines);
        linesChanged = insertLines.length;
        break;

      case 'delete':
        if (params.lineNumber) {
          // Delete single line
          const deleteIdx = params.lineNumber - 1;
          if (deleteIdx < 0 || deleteIdx >= lines.length) {
            throw new Error('Invalid line number for delete operation');
          }
          
          modifiedLines.splice(deleteIdx, 1);
          linesChanged = 1;
        } else if (params.startLine && params.endLine) {
          // Delete range of lines
          const startIdx = params.startLine - 1;
          const endIdx = params.endLine - 1;
          
          if (startIdx < 0 || endIdx >= lines.length || startIdx > endIdx) {
            throw new Error('Invalid line range for delete operation');
          }
          
          const deletedCount = endIdx - startIdx + 1;
          modifiedLines.splice(startIdx, deletedCount);
          linesChanged = deletedCount;
        } else {
          throw new Error('Delete operation requires lineNumber or startLine/endLine');
        }
        break;

      case 'append':
        if (params.content === undefined) {
          throw new Error('Append operation requires content');
        }
        
        const appendLines = params.content.split('\n');
        modifiedLines.push(...appendLines);
        linesChanged = appendLines.length;
        break;

      case 'prepend':
        if (params.content === undefined) {
          throw new Error('Prepend operation requires content');
        }
        
        const prependLines = params.content.split('\n');
        modifiedLines.unshift(...prependLines);
        linesChanged = prependLines.length;
        break;

      default:
        throw new Error(`Unknown operation: ${params.operation}`);
    }

    const modifiedContent = modifiedLines.join('\n');
    const diff = this.generateDiff(originalContent, modifiedContent);
    return {
      originalContent,
      modifiedContent,
      linesChanged,
      diff
    };
  }

  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  private async createBackup(filePath: string, content: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup.${timestamp}`;
    
    await fs.writeFile(backupPath, content, 'utf-8');
    
    logger.debug('Backup created', { originalPath: filePath, backupPath });
    
    return backupPath;
  }

  private generateDiff(original: string, modified: string): string {
    const originalLines = original.split('\n');
    const modifiedLines = modified.split('\n');
    
    const diff: string[] = [];
    const maxLines = Math.max(originalLines.length, modifiedLines.length);
    
    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[i];
      const modifiedLine = modifiedLines[i];
      
      if (originalLine !== modifiedLine) {
        if (originalLine !== undefined) {
          diff.push(`- ${originalLine}`);
        }
        if (modifiedLine !== undefined) {
          diff.push(`+ ${modifiedLine}`);
        }
      }
    }
    
    return diff.join('\n');
  }

  private escapeRegex(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  private sanitizeParams(params: any): any {
    // Remove potentially large content for logging
    const sanitized = { ...params };
    if (sanitized.content && sanitized.content.length > 100) {
      sanitized.content = sanitized.content.substring(0, 100) + '...';
    }
    return sanitized;
  }
}
