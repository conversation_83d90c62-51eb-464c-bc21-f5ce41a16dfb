// Chat session management with <PERSON>en
import { ArienClient, ChatMessage, ChatResponse } from './client.js';
import { ToolRegistry } from '../tools/tool-registry.js';
import { logger } from './logger.js';
import { Turn, TurnManager } from './turn.js';
import { TokenLimitManager } from './tokenLimits.js';
import { ContentGenerator } from './contentGenerator.js';
import { ArienError, ErrorCode } from '../utils/errors.js';
import { getConfig } from '../config/config.js';

export interface ChatSession {
  id: string;
  userId: string;
  messages: ChatMessage[];
  turns: Turn[];
  context: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatOptions {
  sessionId?: string;
  userId?: string;
  provider?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  tools?: boolean;
  web?: boolean;
  autoApprove?: boolean;
}

export class ArienChat {
  private client: ArienClient;
  private toolRegistry: ToolRegistry;
  private turnManager: TurnManager;
  private tokenManager: TokenLimitManager;
  private contentGenerator: ContentGenerator;
  private sessions = new Map<string, ChatSession>();

  constructor(options: ChatOptions = {}) {
    this.client = new ArienClient({
      provider: options.provider as any,
      model: options.model,
      temperature: options.temperature,
      maxTokens: options.maxTokens,
      stream: options.stream
    });

    this.toolRegistry = new ToolRegistry();
    this.turnManager = new TurnManager();
    this.tokenManager = new TokenLimitManager();
    this.contentGenerator = new ContentGenerator();

    // Register default tools if enabled
    if (options.tools !== false) {
      this.registerDefaultTools();
    }
  }

  public async createSession(userId: string, sessionId?: string): Promise<ChatSession> {
    const id = sessionId || this.generateSessionId();
    
    const session: ChatSession = {
      id,
      userId,
      messages: [],
      turns: [],
      context: {},
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.sessions.set(id, session);
    logger.info('Chat session created', { sessionId: id, userId });
    
    return session;
  }

  public getSession(sessionId: string): ChatSession | undefined {
    return this.sessions.get(sessionId);
  }

  public async sendMessage(
    sessionId: string,
    message: string,
    options: Partial<ChatOptions> = {}
  ): Promise<ChatResponse> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new ArienError(ErrorCode.SESSION_NOT_FOUND, `Session ${sessionId} not found`);
    }

    // Create user message
    const userMessage: ChatMessage = {
      role: 'user',
      content: message
    };

    // Add to session
    session.messages.push(userMessage);
    session.updatedAt = new Date();

    // Create turn
    const turn = this.turnManager.createTurn(sessionId, userMessage);

    try {
      // Check token limits
      const tokenCheck = await this.tokenManager.checkTokenLimits(session.messages);
      if (!tokenCheck.withinLimits) {
        // Compress or truncate messages if needed
        session.messages = await this.contentGenerator.compressMessages(session.messages);
      }

      // Get available tools
      const tools = this.toolRegistry.getToolDefinitions();

      // Send to AI
      const response = await this.client.chat(session.messages, tools, {
        temperature: options.temperature,
        maxTokens: options.maxTokens
      });

      // Create assistant message
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: response.content,
        toolCalls: response.toolCalls,
        toolResults: response.toolResults
      };

      // Add to session
      session.messages.push(assistantMessage);
      
      // Complete turn
      this.turnManager.completeTurn(turn.id, assistantMessage, response.usage);
      session.turns.push(turn);

      logger.info('Message processed successfully', {
        sessionId,
        messageLength: message.length,
        responseLength: response.content.length,
        usage: response.usage
      });

      return response;
    } catch (error) {
      this.turnManager.failTurn(turn.id, error as Error);
      logger.error('Message processing failed', { sessionId, error });
      throw error;
    }
  }

  public async *streamMessage(
    sessionId: string,
    message: string,
    options: Partial<ChatOptions> = {}
  ): AsyncGenerator<string, ChatResponse, unknown> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new ArienError(ErrorCode.SESSION_NOT_FOUND, `Session ${sessionId} not found`);
    }

    // Create user message
    const userMessage: ChatMessage = {
      role: 'user',
      content: message
    };

    // Add to session
    session.messages.push(userMessage);
    session.updatedAt = new Date();

    // Create turn
    const turn = this.turnManager.createTurn(sessionId, userMessage);

    try {
      // Check token limits
      const tokenCheck = await this.tokenManager.checkTokenLimits(session.messages);
      if (!tokenCheck.withinLimits) {
        session.messages = await this.contentGenerator.compressMessages(session.messages);
      }

      // Get available tools
      const tools = this.toolRegistry.getToolDefinitions();

      // Stream from AI
      const stream = this.client.streamChat(session.messages, tools, {
        temperature: options.temperature,
        maxTokens: options.maxTokens
      });

      let fullContent = '';
      
      for await (const chunk of stream) {
        fullContent += chunk;
        yield chunk;
      }

      // The stream is consumed, we need to get the final response differently
      // For now, create a basic response
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: fullContent,
        toolCalls: [],
        toolResults: []
      };

      // Add to session
      session.messages.push(assistantMessage);

      // Complete turn
      this.turnManager.completeTurn(turn.id, assistantMessage, undefined);
      session.turns.push(turn);

      return { content: fullContent };
    } catch (error) {
      this.turnManager.failTurn(turn.id, error as Error);
      throw error;
    }
  }

  private registerDefaultTools(): void {
    // Register core tools
    const { ReadFileTool } = require('../tools/read-file.js');
    const { WriteFileTool } = require('../tools/write-file.js');
    const { ShellTool } = require('../tools/shell.js');

    this.toolRegistry.registerTool(new ReadFileTool());
    this.toolRegistry.registerTool(new WriteFileTool());
    this.toolRegistry.registerTool(new ShellTool());

    logger.debug('Default tools registered');
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public deleteSession(sessionId: string): boolean {
    const deleted = this.sessions.delete(sessionId);
    if (deleted) {
      logger.info('Session deleted', { sessionId });
    }
    return deleted;
  }

  public listSessions(userId?: string): ChatSession[] {
    const sessions = Array.from(this.sessions.values());
    return userId ? sessions.filter(s => s.userId === userId) : sessions;
  }

  public getToolRegistry(): ToolRegistry {
    return this.toolRegistry;
  }
}
