// Enhanced file editing tool with validation and backup
import { readFile, writeFile, copyFile, stat, mkdir } from 'fs/promises';
import { join, dirname, basename, extname } from 'path';
import { diffLines, diffChars } from 'diff';
import { logger } from '../core/logger.js';
import { EditCorrector, EditValidationResult } from '../utils/editCorrector.js';
import { validatePath } from '../security/pathSecurity.js';
import { getApprovalSystem, createApprovalRequest } from '../security/approvalSystem.js';
import { ArienError, ErrorCode } from '../utils/errors.js';

export interface EditOptions {
  createBackup?: boolean;
  backupDir?: string;
  validateEdit?: boolean;
  autoCorrect?: boolean;
  encoding?: BufferEncoding;
  mode?: number;
  preserveTimestamps?: boolean;
  dryRun?: boolean;
}

export interface EditResult {
  success: boolean;
  originalContent: string;
  newContent: string;
  backupPath?: string;
  validation?: EditValidationResult;
  diff: string;
  bytesChanged: number;
  linesChanged: number;
  approved: boolean;
  error?: string;
}

export interface EditContext {
  filePath: string;
  language?: string;
  projectType?: string;
  workingDirectory: string;
}

export class EnhancedEdit {
  private editCorrector: EditCorrector;
  private editHistory: Array<{ filePath: string; result: EditResult; timestamp: Date }> = [];

  constructor() {
    this.editCorrector = new EditCorrector();
  }

  public async editFile(
    filePath: string,
    newContent: string,
    options: EditOptions = {}
  ): Promise<EditResult> {
    const startTime = Date.now();

    try {
      // Validate path security
      const pathValidation = await validatePath(filePath, 'write');
      if (!pathValidation.isValid) {
        throw new ArienError(
          ErrorCode.SECURITY_VIOLATION,
          `Path validation failed: ${pathValidation.issues.map(i => i.message).join(', ')}`,
          { filePath, issues: pathValidation.issues }
        );
      }

      // Read original content
      let originalContent = '';
      let fileExists = false;
      
      try {
        originalContent = await readFile(filePath, options.encoding || 'utf-8');
        fileExists = true;
      } catch (error) {
        if ((error as any).code !== 'ENOENT') {
          throw error;
        }
        // File doesn't exist, will be created
      }

      // Validate edit if requested
      let validation: EditValidationResult | undefined;
      let finalContent = newContent;

      if (options.validateEdit !== false) {
        const context: EditContext = {
          filePath,
          language: this.detectLanguage(filePath),
          workingDirectory: dirname(filePath)
        };

        validation = await this.editCorrector.validateEdit(
          originalContent,
          newContent,
          context
        );

        // Apply auto-correction if enabled and needed
        if (options.autoCorrect && validation.correctedEdit) {
          finalContent = validation.correctedEdit.correctedContent;
          logger.info('Auto-correction applied', {
            filePath,
            explanation: validation.correctedEdit.explanation
          });
        }
      }

      // Calculate diff
      const diff = this.generateDiff(originalContent, finalContent);
      const { bytesChanged, linesChanged } = this.calculateChanges(originalContent, finalContent);

      // Determine risk level
      const riskLevel = this.calculateRiskLevel(filePath, originalContent, finalContent, validation);

      // Request approval
      const approvalRequest = createApprovalRequest(
        'file_edit',
        `Edit file: ${filePath}`,
        riskLevel,
        {
          filePaths: [filePath],
          parameters: {
            bytesChanged,
            linesChanged,
            fileExists,
            hasValidationIssues: validation ? validation.issues.length > 0 : false
          },
          estimatedImpact: this.estimateImpact(filePath, originalContent, finalContent),
          reversible: options.createBackup !== false
        }
      );

      const approvalSystem = getApprovalSystem();
      const approval = await approvalSystem.requestApproval(approvalRequest);

      if (!approval.approved) {
        const result: EditResult = {
          success: false,
          originalContent,
          newContent: finalContent,
          diff,
          bytesChanged,
          linesChanged,
          approved: false,
          validation,
          error: `Edit denied: ${approval.reason}`
        };

        this.recordEdit(filePath, result);
        return result;
      }

      // Perform dry run if requested
      if (options.dryRun) {
        const result: EditResult = {
          success: true,
          originalContent,
          newContent: finalContent,
          diff,
          bytesChanged,
          linesChanged,
          approved: true,
          validation
        };

        logger.info('Dry run completed', { filePath, bytesChanged, linesChanged });
        return result;
      }

      // Create backup if requested
      let backupPath: string | undefined;
      if (options.createBackup !== false && fileExists) {
        backupPath = await this.createBackup(filePath, options.backupDir);
      }

      // Ensure directory exists
      await mkdir(dirname(filePath), { recursive: true });

      // Write new content
      await writeFile(filePath, finalContent, {
        encoding: options.encoding || 'utf-8',
        mode: options.mode
      });

      // Preserve timestamps if requested
      if (options.preserveTimestamps && fileExists) {
        try {
          const stats = await stat(backupPath || filePath);
          // Note: Node.js doesn't have a direct way to set timestamps
          // This would require additional implementation
        } catch (error) {
          logger.warn('Failed to preserve timestamps', { filePath, error });
        }
      }

      const result: EditResult = {
        success: true,
        originalContent,
        newContent: finalContent,
        backupPath,
        validation,
        diff,
        bytesChanged,
        linesChanged,
        approved: true
      };

      this.recordEdit(filePath, result);
      
      logger.info('File edited successfully', {
        filePath,
        bytesChanged,
        linesChanged,
        backupCreated: !!backupPath,
        duration: Date.now() - startTime
      });

      return result;
    } catch (error) {
      const result: EditResult = {
        success: false,
        originalContent: '',
        newContent,
        diff: '',
        bytesChanged: 0,
        linesChanged: 0,
        approved: false,
        error: error instanceof Error ? error.message : String(error)
      };

      this.recordEdit(filePath, result);
      
      logger.error('File edit failed', { filePath, error });
      return result;
    }
  }

  public async applyPatch(
    filePath: string,
    patch: string,
    options: EditOptions = {}
  ): Promise<EditResult> {
    try {
      // Read original content
      const originalContent = await readFile(filePath, options.encoding || 'utf-8');
      
      // Apply patch (simplified implementation)
      const newContent = this.applySimplePatch(originalContent, patch);
      
      return this.editFile(filePath, newContent, options);
    } catch (error) {
      throw new ArienError(
        ErrorCode.EXECUTION_ERROR,
        `Failed to apply patch: ${error instanceof Error ? error.message : String(error)}`,
        { filePath, patch }
      );
    }
  }

  public async restoreFromBackup(filePath: string, backupPath: string): Promise<EditResult> {
    try {
      const backupContent = await readFile(backupPath, 'utf-8');
      return this.editFile(filePath, backupContent, { createBackup: false });
    } catch (error) {
      throw new ArienError(
        ErrorCode.EXECUTION_ERROR,
        `Failed to restore from backup: ${error instanceof Error ? error.message : String(error)}`,
        { filePath, backupPath }
      );
    }
  }

  private async createBackup(filePath: string, backupDir?: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = basename(filePath);
    const ext = extname(fileName);
    const nameWithoutExt = basename(fileName, ext);
    
    const backupFileName = `${nameWithoutExt}.${timestamp}${ext}.bak`;
    const backupPath = backupDir 
      ? join(backupDir, backupFileName)
      : join(dirname(filePath), '.backups', backupFileName);

    // Ensure backup directory exists
    await mkdir(dirname(backupPath), { recursive: true });

    // Copy file to backup location
    await copyFile(filePath, backupPath);

    logger.debug('Backup created', { originalPath: filePath, backupPath });
    return backupPath;
  }

  private detectLanguage(filePath: string): string {
    const ext = extname(filePath).toLowerCase();
    const languageMap: Record<string, string> = {
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.py': 'python',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'c',
      '.cs': 'csharp',
      '.go': 'go',
      '.rs': 'rust',
      '.php': 'php',
      '.rb': 'ruby',
      '.html': 'html',
      '.css': 'css',
      '.json': 'json',
      '.xml': 'xml',
      '.yaml': 'yaml',
      '.yml': 'yaml',
      '.md': 'markdown',
      '.sh': 'bash',
      '.ps1': 'powershell'
    };

    return languageMap[ext] || 'text';
  }

  private generateDiff(originalContent: string, newContent: string): string {
    const changes = diffLines(originalContent, newContent);
    
    let diff = '';
    let lineNumber = 1;

    for (const change of changes) {
      const lines = change.value.split('\n');
      if (change.added) {
        for (const line of lines) {
          if (line !== '') {
            diff += `+${lineNumber}: ${line}\n`;
            lineNumber++;
          }
        }
      } else if (change.removed) {
        for (const line of lines) {
          if (line !== '') {
            diff += `-${lineNumber}: ${line}\n`;
          }
        }
      } else {
        lineNumber += lines.length - 1;
      }
    }

    return diff;
  }

  private calculateChanges(originalContent: string, newContent: string): { bytesChanged: number; linesChanged: number } {
    const bytesChanged = Math.abs(newContent.length - originalContent.length);
    
    const originalLines = originalContent.split('\n').length;
    const newLines = newContent.split('\n').length;
    const linesChanged = Math.abs(newLines - originalLines);

    return { bytesChanged, linesChanged };
  }

  private calculateRiskLevel(
    filePath: string,
    originalContent: string,
    newContent: string,
    validation?: EditValidationResult
  ): 'low' | 'medium' | 'high' | 'critical' {
    // Check validation issues
    if (validation?.issues.some(issue => issue.severity === 'critical')) {
      return 'critical';
    }

    // Check file type
    const ext = extname(filePath).toLowerCase();
    const systemFiles = ['.conf', '.config', '.ini', '.cfg', '.env'];
    const executableFiles = ['.sh', '.bat', '.ps1', '.exe'];

    if (systemFiles.includes(ext)) {
      return 'high';
    }

    if (executableFiles.includes(ext)) {
      return 'high';
    }

    // Check content changes
    const { bytesChanged } = this.calculateChanges(originalContent, newContent);
    const changePercentage = originalContent.length > 0 
      ? (bytesChanged / originalContent.length) * 100 
      : 100;

    if (changePercentage > 50) {
      return 'medium';
    }

    return 'low';
  }

  private estimateImpact(filePath: string, originalContent: string, newContent: string): string {
    const { bytesChanged, linesChanged } = this.calculateChanges(originalContent, newContent);
    const ext = extname(filePath).toLowerCase();

    if (['.sh', '.bat', '.ps1'].includes(ext)) {
      return `Script modification - ${linesChanged} lines, ${bytesChanged} bytes changed`;
    }

    if (['.conf', '.config', '.ini', '.env'].includes(ext)) {
      return `Configuration change - ${linesChanged} lines, ${bytesChanged} bytes changed`;
    }

    return `File modification - ${linesChanged} lines, ${bytesChanged} bytes changed`;
  }

  private applySimplePatch(content: string, patch: string): string {
    // This is a simplified patch application
    // In a real implementation, you'd want to use a proper patch parser
    const lines = content.split('\n');
    const patchLines = patch.split('\n');

    for (const patchLine of patchLines) {
      if (patchLine.startsWith('+')) {
        // Add line (simplified)
        lines.push(patchLine.substring(1));
      } else if (patchLine.startsWith('-')) {
        // Remove line (simplified)
        const lineToRemove = patchLine.substring(1);
        const index = lines.indexOf(lineToRemove);
        if (index >= 0) {
          lines.splice(index, 1);
        }
      }
    }

    return lines.join('\n');
  }

  private recordEdit(filePath: string, result: EditResult): void {
    this.editHistory.push({
      filePath,
      result,
      timestamp: new Date()
    });

    // Keep only last 50 edits
    if (this.editHistory.length > 50) {
      this.editHistory = this.editHistory.slice(-50);
    }
  }

  public getEditHistory(filePath?: string): Array<{ filePath: string; result: EditResult; timestamp: Date }> {
    const history = [...this.editHistory].reverse();
    return filePath 
      ? history.filter(entry => entry.filePath === filePath)
      : history;
  }

  public clearHistory(): void {
    this.editHistory = [];
  }
}

// Global enhanced edit instance
let globalEnhancedEdit: EnhancedEdit | null = null;

export function getEnhancedEdit(): EnhancedEdit {
  if (!globalEnhancedEdit) {
    globalEnhancedEdit = new EnhancedEdit();
  }
  return globalEnhancedEdit;
}

export function setEnhancedEdit(edit: EnhancedEdit): void {
  globalEnhancedEdit = edit;
}
