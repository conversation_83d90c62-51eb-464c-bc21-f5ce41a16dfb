// Enhanced shell command execution tool with security and monitoring
import { spawn, ChildProcess } from 'child_process';
import { logger } from '../core/logger.js';
import { CommandValidator, CommandValidationResult } from '../security/commandValidator.js';
import { getApprovalSystem, createApprovalRequest } from '../security/approvalSystem.js';
import { ArienError, ErrorCode } from '../utils/errors.js';

export interface ShellExecutionOptions {
  cwd?: string;
  env?: Record<string, string>;
  timeout?: number;
  shell?: boolean;
  input?: string;
  maxBuffer?: number;
  killSignal?: string;
  uid?: number;
  gid?: number;
  windowsHide?: boolean;
}

export interface ShellExecutionResult {
  success: boolean;
  stdout: string;
  stderr: string;
  exitCode: number | null;
  signal: string | null;
  duration: number;
  command: string;
  pid?: number;
  validation?: CommandValidationResult;
  approved: boolean;
}

export interface ShellStreamOptions extends ShellExecutionOptions {
  onStdout?: (data: string) => void;
  onStderr?: (data: string) => void;
  onExit?: (code: number | null, signal: string | null) => void;
}

export class EnhancedShell {
  private validator: CommandValidator;
  private runningProcesses = new Map<string, ChildProcess>();
  private executionHistory: ShellExecutionResult[] = [];

  constructor() {
    this.validator = new CommandValidator();
  }

  public async execute(
    command: string,
    options: ShellExecutionOptions = {}
  ): Promise<ShellExecutionResult> {
    const startTime = Date.now();
    const executionId = `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    try {
      // Validate command
      const validation = this.validator.validateCommand(command, {
        workingDirectory: options.cwd || process.cwd(),
        userId: 'default',
        environment: options.env || {}
      });

      // Request approval if needed
      const approvalRequest = createApprovalRequest(
        'shell_execute',
        `Execute shell command: ${command}`,
        validation.riskLevel,
        {
          command,
          parameters: options,
          estimatedImpact: this.estimateImpact(command, validation),
          reversible: this.isReversible(command)
        }
      );

      const approvalSystem = getApprovalSystem();
      const approval = await approvalSystem.requestApproval(approvalRequest);

      if (!approval.approved) {
        const result: ShellExecutionResult = {
          success: false,
          stdout: '',
          stderr: `Command execution denied: ${approval.reason}`,
          exitCode: -1,
          signal: null,
          duration: Date.now() - startTime,
          command,
          validation,
          approved: false
        };
        
        this.recordExecution(result);
        return result;
      }

      // Execute command
      const result = await this.executeCommand(command, options, executionId);
      result.validation = validation;
      result.approved = true;

      this.recordExecution(result);
      return result;
    } catch (error) {
      const result: ShellExecutionResult = {
        success: false,
        stdout: '',
        stderr: error instanceof Error ? error.message : String(error),
        exitCode: -1,
        signal: null,
        duration: Date.now() - startTime,
        command,
        approved: false
      };

      this.recordExecution(result);
      return result;
    }
  }

  public async executeStream(
    command: string,
    options: ShellStreamOptions = {}
  ): Promise<ShellExecutionResult> {
    const startTime = Date.now();
    const executionId = `stream-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    try {
      // Validate command
      const validation = this.validator.validateCommand(command, {
        workingDirectory: options.cwd || process.cwd(),
        userId: 'default',
        environment: options.env || {}
      });

      // Request approval
      const approvalRequest = createApprovalRequest(
        'shell_execute_stream',
        `Execute streaming shell command: ${command}`,
        validation.riskLevel,
        {
          command,
          parameters: options,
          estimatedImpact: this.estimateImpact(command, validation),
          reversible: this.isReversible(command)
        }
      );

      const approvalSystem = getApprovalSystem();
      const approval = await approvalSystem.requestApproval(approvalRequest);

      if (!approval.approved) {
        const result: ShellExecutionResult = {
          success: false,
          stdout: '',
          stderr: `Command execution denied: ${approval.reason}`,
          exitCode: -1,
          signal: null,
          duration: Date.now() - startTime,
          command,
          validation,
          approved: false
        };
        
        this.recordExecution(result);
        return result;
      }

      // Execute with streaming
      const result = await this.executeStreamingCommand(command, options, executionId);
      result.validation = validation;
      result.approved = true;

      this.recordExecution(result);
      return result;
    } catch (error) {
      const result: ShellExecutionResult = {
        success: false,
        stdout: '',
        stderr: error instanceof Error ? error.message : String(error),
        exitCode: -1,
        signal: null,
        duration: Date.now() - startTime,
        command,
        approved: false
      };

      this.recordExecution(result);
      return result;
    }
  }

  private async executeCommand(
    command: string,
    options: ShellExecutionOptions,
    executionId: string
  ): Promise<ShellExecutionResult> {
    const startTime = Date.now();

    return new Promise((resolve, reject) => {
      const {
        cwd = process.cwd(),
        env = process.env,
        timeout = 30000,
        shell = true,
        input,
        maxBuffer = 1024 * 1024,
        killSignal = 'SIGTERM',
        uid,
        gid,
        windowsHide = true
      } = options;

      let stdout = '';
      let stderr = '';
      let timedOut = false;

      const childProcess = spawn(shell ? process.platform === 'win32' ? 'cmd' : 'sh' : command, 
        shell ? [process.platform === 'win32' ? '/c' : '-c', command] : [], {
        cwd,
        env: { ...env },
        stdio: ['pipe', 'pipe', 'pipe'],
        uid,
        gid,
        windowsHide
      });

      this.runningProcesses.set(executionId, childProcess);

      // Set up timeout
      const timeoutId = setTimeout(() => {
        timedOut = true;
        childProcess.kill(killSignal);
        setTimeout(() => {
          if (!childProcess.killed) {
            childProcess.kill('SIGKILL');
          }
        }, 5000);
      }, timeout);

      // Handle stdout
      childProcess.stdout?.on('data', (data: Buffer) => {
        const chunk = data.toString();
        stdout += chunk;
        
        if (stdout.length > maxBuffer) {
          childProcess.kill(killSignal);
          stderr += '\nOutput exceeded maximum buffer size\n';
        }
      });

      // Handle stderr
      childProcess.stderr?.on('data', (data: Buffer) => {
        const chunk = data.toString();
        stderr += chunk;
        
        if (stderr.length > maxBuffer) {
          childProcess.kill(killSignal);
          stderr += '\nError output exceeded maximum buffer size\n';
        }
      });

      // Handle input
      if (input && childProcess.stdin) {
        childProcess.stdin.write(input);
        childProcess.stdin.end();
      }

      // Handle process exit
      childProcess.on('close', (code, signal) => {
        clearTimeout(timeoutId);
        this.runningProcesses.delete(executionId);

        const result: ShellExecutionResult = {
          success: code === 0 && !timedOut,
          stdout: stdout.trim(),
          stderr: stderr.trim(),
          exitCode: code,
          signal,
          duration: Date.now() - startTime,
          command,
          pid: childProcess.pid,
          approved: true
        };

        if (timedOut) {
          result.stderr += '\nCommand timed out';
        }

        resolve(result);
      });

      // Handle errors
      childProcess.on('error', (error) => {
        clearTimeout(timeoutId);
        this.runningProcesses.delete(executionId);
        
        reject(new ArienError(
          ErrorCode.EXECUTION_ERROR,
          `Command execution failed: ${error.message}`,
          { command, error }
        ));
      });
    });
  }

  private async executeStreamingCommand(
    command: string,
    options: ShellStreamOptions,
    executionId: string
  ): Promise<ShellExecutionResult> {
    const startTime = Date.now();

    return new Promise((resolve, reject) => {
      const {
        cwd = process.cwd(),
        env = process.env,
        timeout = 30000,
        shell = true,
        input,
        killSignal = 'SIGTERM',
        uid,
        gid,
        windowsHide = true,
        onStdout,
        onStderr,
        onExit
      } = options;

      let stdout = '';
      let stderr = '';
      let timedOut = false;

      const childProcess = spawn(shell ? process.platform === 'win32' ? 'cmd' : 'sh' : command, 
        shell ? [process.platform === 'win32' ? '/c' : '-c', command] : [], {
        cwd,
        env: { ...env },
        stdio: ['pipe', 'pipe', 'pipe'],
        uid,
        gid,
        windowsHide
      });

      this.runningProcesses.set(executionId, childProcess);

      // Set up timeout
      const timeoutId = setTimeout(() => {
        timedOut = true;
        childProcess.kill(killSignal);
        setTimeout(() => {
          if (!childProcess.killed) {
            childProcess.kill('SIGKILL');
          }
        }, 5000);
      }, timeout);

      // Handle stdout with streaming
      childProcess.stdout?.on('data', (data: Buffer) => {
        const chunk = data.toString();
        stdout += chunk;
        onStdout?.(chunk);
      });

      // Handle stderr with streaming
      childProcess.stderr?.on('data', (data: Buffer) => {
        const chunk = data.toString();
        stderr += chunk;
        onStderr?.(chunk);
      });

      // Handle input
      if (input && childProcess.stdin) {
        childProcess.stdin.write(input);
        childProcess.stdin.end();
      }

      // Handle process exit
      childProcess.on('close', (code, signal) => {
        clearTimeout(timeoutId);
        this.runningProcesses.delete(executionId);

        const result: ShellExecutionResult = {
          success: code === 0 && !timedOut,
          stdout: stdout.trim(),
          stderr: stderr.trim(),
          exitCode: code,
          signal,
          duration: Date.now() - startTime,
          command,
          pid: childProcess.pid,
          approved: true
        };

        if (timedOut) {
          result.stderr += '\nCommand timed out';
        }

        onExit?.(code, signal);
        resolve(result);
      });

      // Handle errors
      childProcess.on('error', (error) => {
        clearTimeout(timeoutId);
        this.runningProcesses.delete(executionId);
        
        reject(new ArienError(
          ErrorCode.EXECUTION_ERROR,
          `Streaming command execution failed: ${error.message}`,
          { command, error }
        ));
      });
    });
  }

  public killProcess(executionId: string, signal: string = 'SIGTERM'): boolean {
    const process = this.runningProcesses.get(executionId);
    if (process) {
      process.kill(signal);
      return true;
    }
    return false;
  }

  public killAllProcesses(signal: string = 'SIGTERM'): number {
    let killed = 0;
    for (const [id, process] of this.runningProcesses) {
      process.kill(signal);
      killed++;
    }
    return killed;
  }

  public getRunningProcesses(): Array<{ id: string; pid?: number; command: string }> {
    const processes: Array<{ id: string; pid?: number; command: string }> = [];
    
    for (const [id, process] of this.runningProcesses) {
      // We need to track the command somehow - this would need to be enhanced
      processes.push({
        id,
        pid: process.pid,
        command: 'unknown' // Would need to store this when creating the process
      });
    }
    
    return processes;
  }

  public getExecutionHistory(limit?: number): ShellExecutionResult[] {
    const history = [...this.executionHistory].reverse();
    return limit ? history.slice(0, limit) : history;
  }

  public clearHistory(): void {
    this.executionHistory = [];
  }

  private estimateImpact(command: string, validation: CommandValidationResult): string {
    if (validation.riskLevel === 'critical') {
      return 'High - May cause system damage or data loss';
    } else if (validation.riskLevel === 'high') {
      return 'Medium - May modify system files or settings';
    } else if (validation.riskLevel === 'medium') {
      return 'Low - May modify user files';
    } else {
      return 'Minimal - Read-only or safe operations';
    }
  }

  private isReversible(command: string): boolean {
    const irreversiblePatterns = [
      /rm\s+.*-rf/,
      /del\s+.*\/s/,
      /format/,
      /fdisk/,
      /mkfs/,
      /dd\s+.*of=/,
      /shred/
    ];

    return !irreversiblePatterns.some(pattern => pattern.test(command));
  }

  private recordExecution(result: ShellExecutionResult): void {
    this.executionHistory.push(result);
    
    // Keep only last 100 executions
    if (this.executionHistory.length > 100) {
      this.executionHistory = this.executionHistory.slice(-100);
    }

    logger.debug('Shell execution recorded', {
      command: result.command,
      success: result.success,
      duration: result.duration,
      approved: result.approved
    });
  }
}

// Global enhanced shell instance
let globalEnhancedShell: EnhancedShell | null = null;

export function getEnhancedShell(): EnhancedShell {
  if (!globalEnhancedShell) {
    globalEnhancedShell = new EnhancedShell();
  }
  return globalEnhancedShell;
}

export function setEnhancedShell(shell: EnhancedShell): void {
  globalEnhancedShell = shell;
}
