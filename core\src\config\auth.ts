// Authentication method validation
import { z } from 'zod';
import { Provider, PROVIDERS } from './models.js';
import { logger } from '../core/logger.js';

export interface AuthMethod {
  type: 'api_key' | 'oauth' | 'service_account';
  provider: Provider;
  credentials: Record<string, string>;
  metadata?: Record<string, any>;
  expiresAt?: Date;
  scopes?: string[];
}

export interface AuthValidationResult {
  valid: boolean;
  provider: Provider;
  method: string;
  errors: string[];
  warnings: string[];
  capabilities?: string[];
}

export interface AuthConfig {
  methods: Record<string, AuthMethod>;
  defaultMethod?: string;
  autoRefresh: boolean;
  encryptCredentials: boolean;
}

const AuthMethodSchema = z.object({
  type: z.enum(['api_key', 'oauth', 'service_account']),
  provider: z.nativeEnum(PROVIDERS),
  credentials: z.record(z.string()),
  metadata: z.record(z.any()).optional(),
  expiresAt: z.string().optional().transform(str => str ? new Date(str) : undefined),
  scopes: z.array(z.string()).optional()
});

const AuthConfigSchema = z.object({
  methods: z.record(AuthMethodSchema),
  defaultMethod: z.string().optional(),
  autoRefresh: z.boolean().default(true),
  encryptCredentials: z.boolean().default(true)
});

export class AuthValidator {
  private static readonly REQUIRED_CREDENTIALS = {
    [PROVIDERS.DEEPSEEK]: {
      api_key: ['apiKey']
    },
    [PROVIDERS.OPENAI]: {
      api_key: ['apiKey'],
      oauth: ['clientId', 'clientSecret', 'accessToken']
    },
    [PROVIDERS.ANTHROPIC]: {
      api_key: ['apiKey']
    },
    [PROVIDERS.GOOGLE]: {
      api_key: ['apiKey'],
      service_account: ['projectId', 'privateKey', 'clientEmail']
    }
  };

  public static async validateAuth(method: AuthMethod): Promise<AuthValidationResult> {
    const result: AuthValidationResult = {
      valid: false,
      provider: method.provider,
      method: method.type,
      errors: [],
      warnings: [],
      capabilities: []
    };

    try {
      // Validate provider support
      if (!this.REQUIRED_CREDENTIALS[method.provider]) {
        result.errors.push(`Provider ${method.provider} is not supported`);
        return result;
      }

      // Validate auth method for provider
      const providerMethods = this.REQUIRED_CREDENTIALS[method.provider];
      const methodCredentials = providerMethods[method.type as keyof typeof providerMethods];
      if (!methodCredentials) {
        result.errors.push(`Auth method ${method.type} is not supported for provider ${method.provider}`);
        return result;
      }

      // Validate required credentials
      const requiredCreds = methodCredentials;
      for (const cred of requiredCreds) {
        if (!method.credentials[cred] || method.credentials[cred].trim() === '') {
          result.errors.push(`Missing required credential: ${cred}`);
        }
      }

      // Check expiration
      if (method.expiresAt && method.expiresAt < new Date()) {
        result.errors.push('Authentication credentials have expired');
      }

      // Validate specific auth types
      switch (method.type) {
        case 'api_key':
          await this.validateApiKey(method, result);
          break;
        case 'oauth':
          await this.validateOAuth(method, result);
          break;
        case 'service_account':
          await this.validateServiceAccount(method, result);
          break;
      }

      result.valid = result.errors.length === 0;

      logger.debug('Auth validation completed', {
        provider: method.provider,
        method: method.type,
        valid: result.valid,
        errorCount: result.errors.length
      });

      return result;

    } catch (error) {
      result.errors.push(`Validation failed: ${error instanceof Error ? error.message : String(error)}`);
      return result;
    }
  }

  private static async validateApiKey(method: AuthMethod, result: AuthValidationResult): Promise<void> {
    const apiKey = method.credentials.apiKey;
    
    // Basic format validation
    switch (method.provider) {
      case PROVIDERS.OPENAI:
        if (!apiKey.startsWith('sk-')) {
          result.warnings.push('OpenAI API key should start with "sk-"');
        }
        if (apiKey.length < 40) {
          result.warnings.push('OpenAI API key appears to be too short');
        }
        break;

      case PROVIDERS.ANTHROPIC:
        if (!apiKey.startsWith('sk-ant-')) {
          result.warnings.push('Anthropic API key should start with "sk-ant-"');
        }
        break;

      case PROVIDERS.DEEPSEEK:
        if (apiKey.length < 20) {
          result.warnings.push('DeepSeek API key appears to be too short');
        }
        break;

      case PROVIDERS.GOOGLE:
        if (apiKey.length < 30) {
          result.warnings.push('Google API key appears to be too short');
        }
        break;
    }

    // Test API key by making a simple request
    try {
      const isValid = await this.testApiKey(method.provider, apiKey);
      if (!isValid) {
        result.errors.push('API key validation failed - key may be invalid or expired');
      } else {
        result.capabilities = await this.getApiCapabilities(method.provider, apiKey);
      }
    } catch (error) {
      result.warnings.push(`Could not test API key: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private static async validateOAuth(method: AuthMethod, result: AuthValidationResult): Promise<void> {
    const { clientId, clientSecret, accessToken, refreshToken } = method.credentials;

    // Validate OAuth credentials format
    if (clientId && clientId.length < 10) {
      result.warnings.push('Client ID appears to be too short');
    }

    if (clientSecret && clientSecret.length < 10) {
      result.warnings.push('Client secret appears to be too short');
    }

    if (accessToken) {
      // Check if access token is expired (if we can determine this)
      try {
        const tokenInfo = await this.validateAccessToken(method.provider, accessToken);
        if (!tokenInfo.valid) {
          if (refreshToken) {
            result.warnings.push('Access token is expired but refresh token is available');
          } else {
            result.errors.push('Access token is expired and no refresh token available');
          }
        } else {
          result.capabilities = tokenInfo.scopes || [];
        }
      } catch (error) {
        result.warnings.push(`Could not validate access token: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
  }

  private static async validateServiceAccount(method: AuthMethod, result: AuthValidationResult): Promise<void> {
    const { projectId, privateKey, clientEmail } = method.credentials;

    // Validate service account format
    if (projectId && !/^[a-z][a-z0-9-]*[a-z0-9]$/.test(projectId)) {
      result.warnings.push('Project ID format appears invalid');
    }

    if (clientEmail && !clientEmail.includes('@') && !clientEmail.includes('.iam.gserviceaccount.com')) {
      result.warnings.push('Client email format appears invalid for service account');
    }

    if (privateKey) {
      if (!privateKey.includes('BEGIN PRIVATE KEY') && !privateKey.includes('BEGIN RSA PRIVATE KEY')) {
        result.errors.push('Private key format is invalid');
      }
    }

    // Test service account authentication
    try {
      const isValid = await this.testServiceAccount(method);
      if (!isValid) {
        result.errors.push('Service account authentication failed');
      }
    } catch (error) {
      result.warnings.push(`Could not test service account: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private static async testApiKey(provider: Provider, apiKey: string): Promise<boolean> {
    // This would make actual API calls to test the key
    // For now, we'll do basic validation
    try {
      switch (provider) {
        case PROVIDERS.OPENAI:
          // Could make a call to /v1/models endpoint
          return apiKey.length > 40 && apiKey.startsWith('sk-');
        
        case PROVIDERS.ANTHROPIC:
          // Could make a call to /v1/messages endpoint with minimal request
          return apiKey.length > 40 && apiKey.startsWith('sk-ant-');
        
        case PROVIDERS.DEEPSEEK:
          // Could make a call to their API endpoint
          return apiKey.length > 20;
        
        case PROVIDERS.GOOGLE:
          // Could make a call to their API endpoint
          return apiKey.length > 30;
        
        default:
          return false;
      }
    } catch {
      return false;
    }
  }

  private static async getApiCapabilities(provider: Provider, apiKey: string): Promise<string[]> {
    // This would query the API to determine available capabilities
    const capabilities: string[] = [];

    switch (provider) {
      case PROVIDERS.OPENAI:
        capabilities.push('chat', 'completions', 'embeddings', 'images');
        break;
      case PROVIDERS.ANTHROPIC:
        capabilities.push('chat', 'completions');
        break;
      case PROVIDERS.DEEPSEEK:
        capabilities.push('chat', 'completions');
        break;
      case PROVIDERS.GOOGLE:
        capabilities.push('chat', 'completions', 'embeddings');
        break;
    }

    return capabilities;
  }

  private static async validateAccessToken(provider: Provider, accessToken: string): Promise<{ valid: boolean; scopes?: string[] }> {
    // This would validate the OAuth access token
    // For now, return basic validation
    return {
      valid: accessToken.length > 20,
      scopes: ['read', 'write']
    };
  }

  private static async testServiceAccount(method: AuthMethod): Promise<boolean> {
    // This would test the service account authentication
    // For now, do basic validation
    const { projectId, privateKey, clientEmail } = method.credentials;
    return !!(projectId && privateKey && clientEmail);
  }

  public static getRequiredCredentials(provider: Provider, authType: string): string[] {
    const providerMethods = this.REQUIRED_CREDENTIALS[provider];
    return providerMethods?.[authType as keyof typeof providerMethods] || [];
  }

  public static getSupportedAuthMethods(provider: Provider): string[] {
    const providerMethods = this.REQUIRED_CREDENTIALS[provider];
    return providerMethods ? Object.keys(providerMethods) : [];
  }

  public static createAuthMethod(
    provider: Provider,
    type: 'api_key' | 'oauth' | 'service_account',
    credentials: Record<string, string>,
    options: {
      scopes?: string[];
      expiresAt?: Date;
      metadata?: Record<string, any>;
    } = {}
  ): AuthMethod {
    return {
      type,
      provider,
      credentials,
      scopes: options.scopes,
      expiresAt: options.expiresAt,
      metadata: options.metadata
    };
  }
}

export class AuthManager {
  private authConfig: AuthConfig;

  constructor(authConfig: AuthConfig = { methods: {}, autoRefresh: true, encryptCredentials: true }) {
    this.authConfig = AuthConfigSchema.parse(authConfig);
  }

  public async addAuthMethod(name: string, method: AuthMethod): Promise<AuthValidationResult> {
    const validation = await AuthValidator.validateAuth(method);
    
    if (validation.valid) {
      this.authConfig.methods[name] = method;
      
      // Set as default if it's the first method
      if (!this.authConfig.defaultMethod) {
        this.authConfig.defaultMethod = name;
      }

      logger.info('Auth method added', {
        name,
        provider: method.provider,
        type: method.type
      });
    }

    return validation;
  }

  public removeAuthMethod(name: string): boolean {
    if (this.authConfig.methods[name]) {
      delete this.authConfig.methods[name];
      
      // Update default if we removed it
      if (this.authConfig.defaultMethod === name) {
        const remainingMethods = Object.keys(this.authConfig.methods);
        this.authConfig.defaultMethod = remainingMethods.length > 0 ? remainingMethods[0] : undefined;
      }

      logger.info('Auth method removed', { name });
      return true;
    }
    return false;
  }

  public getAuthMethod(name: string): AuthMethod | undefined {
    return this.authConfig.methods[name];
  }

  public getDefaultAuthMethod(): AuthMethod | undefined {
    return this.authConfig.defaultMethod ? this.authConfig.methods[this.authConfig.defaultMethod] : undefined;
  }

  public listAuthMethods(): Array<{ name: string; method: AuthMethod }> {
    return Object.entries(this.authConfig.methods).map(([name, method]) => ({ name, method }));
  }

  public async validateAllMethods(): Promise<Record<string, AuthValidationResult>> {
    const results: Record<string, AuthValidationResult> = {};
    
    for (const [name, method] of Object.entries(this.authConfig.methods)) {
      results[name] = await AuthValidator.validateAuth(method);
    }

    return results;
  }

  public getConfig(): AuthConfig {
    return { ...this.authConfig };
  }

  public updateConfig(updates: Partial<AuthConfig>): void {
    this.authConfig = AuthConfigSchema.parse({ ...this.authConfig, ...updates });
  }
}
