// Project analysis and understanding tool
import { readFile, stat, readdir } from 'fs/promises';
import { join, extname, basename, relative } from 'path';
import { logger } from '../core/logger.js';
import { searchFiles } from '../utils/bfsFileSearch.js';
import { ArienError, ErrorCode } from '../utils/errors.js';

export interface ProjectAnalysis {
  projectType: string;
  languages: LanguageInfo[];
  structure: ProjectStructure;
  dependencies: DependencyInfo;
  buildSystem: BuildSystemInfo;
  documentation: DocumentationInfo;
  codeMetrics: CodeMetrics;
  recommendations: string[];
}

export interface LanguageInfo {
  language: string;
  fileCount: number;
  lineCount: number;
  percentage: number;
  mainFiles: string[];
  frameworks?: string[];
}

export interface ProjectStructure {
  rootFiles: string[];
  directories: DirectoryInfo[];
  entryPoints: string[];
  configFiles: string[];
  testFiles: string[];
  documentationFiles: string[];
}

export interface DirectoryInfo {
  name: string;
  path: string;
  purpose: string;
  fileCount: number;
  subdirectories: string[];
}

export interface DependencyInfo {
  packageManager: string;
  dependencies: Array<{ name: string; version: string; type: 'production' | 'development' }>;
  vulnerabilities?: Array<{ name: string; severity: string; description: string }>;
  outdated?: Array<{ name: string; current: string; latest: string }>;
}

export interface BuildSystemInfo {
  type: string;
  configFiles: string[];
  scripts: Array<{ name: string; command: string }>;
  targets?: string[];
}

export interface DocumentationInfo {
  hasReadme: boolean;
  readmeQuality: 'poor' | 'basic' | 'good' | 'excellent';
  hasChangelog: boolean;
  hasContributing: boolean;
  hasLicense: boolean;
  apiDocumentation: boolean;
  codeComments: number;
}

export interface CodeMetrics {
  totalFiles: number;
  totalLines: number;
  codeLines: number;
  commentLines: number;
  blankLines: number;
  complexity: {
    average: number;
    highest: number;
    highestFile: string;
  };
  duplication: {
    percentage: number;
    duplicatedLines: number;
  };
}

export class ProjectAnalyzer {
  private languageDetectors = new Map<string, RegExp[]>([
    ['javascript', [/import\s+.*from/, /require\s*\(/, /module\.exports/, /export\s+(default\s+)?/]],
    ['typescript', [/interface\s+\w+/, /type\s+\w+\s*=/, /enum\s+\w+/, /:\s*\w+(\[\])?/]],
    ['python', [/def\s+\w+\s*\(/, /class\s+\w+/, /import\s+\w+/, /from\s+\w+\s+import/]],
    ['java', [/public\s+class\s+\w+/, /package\s+[\w.]+/, /import\s+[\w.]+/]],
    ['csharp', [/namespace\s+\w+/, /public\s+class\s+\w+/, /using\s+\w+/]],
    ['go', [/package\s+\w+/, /func\s+\w+\s*\(/, /import\s+\(/]],
    ['rust', [/fn\s+\w+\s*\(/, /struct\s+\w+/, /impl\s+\w+/, /use\s+\w+/]],
    ['php', [/<\?php/, /namespace\s+\w+/, /class\s+\w+/, /function\s+\w+/]],
    ['ruby', [/def\s+\w+/, /class\s+\w+/, /module\s+\w+/, /require\s+/]]
  ]);

  private frameworkDetectors = new Map<string, RegExp[]>([
    ['react', [/import.*react/i, /from\s+['"]react['"]/, /jsx/i]],
    ['vue', [/import.*vue/i, /<template>/, /<script>/]],
    ['angular', [/import.*@angular/i, /@Component/, /@Injectable/]],
    ['express', [/require\s*\(\s*['"]express['"]/, /import.*express/]],
    ['django', [/from\s+django/, /import\s+django/]],
    ['flask', [/from\s+flask/, /import\s+flask/]],
    ['spring', [/import.*springframework/, /@SpringBootApplication/]],
    ['laravel', [/use\s+Illuminate/, /Artisan::/]]
  ]);

  public async analyzeProject(rootPath: string): Promise<ProjectAnalysis> {
    const startTime = Date.now();
    
    try {
      logger.debug('Starting project analysis', { rootPath });

      // Get all files in the project
      const files = await this.getAllFiles(rootPath);
      
      // Analyze different aspects
      const [
        projectType,
        languages,
        structure,
        dependencies,
        buildSystem,
        documentation,
        codeMetrics
      ] = await Promise.all([
        this.detectProjectType(rootPath, files),
        this.analyzeLanguages(files),
        this.analyzeStructure(rootPath, files),
        this.analyzeDependencies(rootPath),
        this.analyzeBuildSystem(rootPath),
        this.analyzeDocumentation(rootPath, files),
        this.calculateCodeMetrics(files)
      ]);

      // Generate recommendations
      const recommendations = this.generateRecommendations({
        projectType,
        languages,
        structure,
        dependencies,
        buildSystem,
        documentation,
        codeMetrics
      });

      const analysis: ProjectAnalysis = {
        projectType,
        languages,
        structure,
        dependencies,
        buildSystem,
        documentation,
        codeMetrics,
        recommendations
      };

      logger.debug('Project analysis completed', {
        rootPath,
        duration: Date.now() - startTime,
        projectType,
        languageCount: languages.length
      });

      return analysis;
    } catch (error) {
      logger.error('Project analysis failed', { rootPath, error });
      throw new ArienError(
        ErrorCode.EXECUTION_ERROR,
        `Project analysis failed: ${error instanceof Error ? error.message : String(error)}`,
        { rootPath }
      );
    }
  }

  private async getAllFiles(rootPath: string): Promise<Array<{ path: string; relativePath: string }>> {
    const files = await searchFiles(rootPath, {
      maxResults: 10000,
      includeDirectories: false,
      includeHidden: false,
      excludePatterns: ['node_modules/**', '.git/**', 'dist/**', 'build/**', '*.log']
    });

    return files.map(file => ({
      path: file.path,
      relativePath: file.relativePath
    }));
  }

  private async detectProjectType(rootPath: string, files: Array<{ path: string; relativePath: string }>): Promise<string> {
    const rootFiles = files.filter(f => !f.relativePath.includes('/') && !f.relativePath.includes('\\'));
    const fileNames = rootFiles.map(f => basename(f.path).toLowerCase());

    // Check for specific project indicators
    if (fileNames.includes('package.json')) {
      const packageJson = await this.readJsonFile(join(rootPath, 'package.json'));
      if (packageJson?.dependencies?.react || packageJson?.devDependencies?.react) {
        return 'React Application';
      }
      if (packageJson?.dependencies?.vue || packageJson?.devDependencies?.vue) {
        return 'Vue.js Application';
      }
      if (packageJson?.dependencies?.express) {
        return 'Node.js/Express Application';
      }
      return 'Node.js Project';
    }

    if (fileNames.includes('requirements.txt') || fileNames.includes('pyproject.toml')) {
      return 'Python Project';
    }

    if (fileNames.includes('pom.xml') || fileNames.includes('build.gradle')) {
      return 'Java Project';
    }

    if (fileNames.includes('cargo.toml')) {
      return 'Rust Project';
    }

    if (fileNames.includes('go.mod')) {
      return 'Go Project';
    }

    if (fileNames.includes('composer.json')) {
      return 'PHP Project';
    }

    if (fileNames.some(name => name.endsWith('.csproj') || name.endsWith('.sln'))) {
      return 'C# Project';
    }

    // Fallback to file extension analysis
    const extensions = files.map(f => extname(f.path).toLowerCase());
    const extensionCounts = extensions.reduce((acc, ext) => {
      acc[ext] = (acc[ext] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const mostCommonExt = Object.entries(extensionCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0];

    const extensionToType: Record<string, string> = {
      '.js': 'JavaScript Project',
      '.ts': 'TypeScript Project',
      '.py': 'Python Project',
      '.java': 'Java Project',
      '.cs': 'C# Project',
      '.go': 'Go Project',
      '.rs': 'Rust Project',
      '.php': 'PHP Project',
      '.rb': 'Ruby Project'
    };

    return extensionToType[mostCommonExt] || 'Unknown Project Type';
  }

  private async analyzeLanguages(files: Array<{ path: string; relativePath: string }>): Promise<LanguageInfo[]> {
    const languageStats = new Map<string, { files: string[]; lines: number }>();

    for (const file of files) {
      const ext = extname(file.path).toLowerCase();
      const language = this.getLanguageFromExtension(ext);
      
      if (language) {
        if (!languageStats.has(language)) {
          languageStats.set(language, { files: [], lines: 0 });
        }
        
        const stats = languageStats.get(language)!;
        stats.files.push(file.relativePath);
        
        try {
          const content = await readFile(file.path, 'utf-8');
          stats.lines += content.split('\n').length;
        } catch (error) {
          // Skip files that can't be read
        }
      }
    }

    const totalLines = Array.from(languageStats.values()).reduce((sum, stats) => sum + stats.lines, 0);
    
    const languages: LanguageInfo[] = Array.from(languageStats.entries()).map(([language, stats]) => ({
      language,
      fileCount: stats.files.length,
      lineCount: stats.lines,
      percentage: totalLines > 0 ? (stats.lines / totalLines) * 100 : 0,
      mainFiles: stats.files.slice(0, 5), // Top 5 files
      frameworks: [] // Would be populated by framework detection
    }));

    // Sort by line count
    languages.sort((a, b) => b.lineCount - a.lineCount);

    // Detect frameworks for each language
    for (const langInfo of languages) {
      langInfo.frameworks = await this.detectFrameworks(langInfo.language, files);
    }

    return languages;
  }

  private async analyzeStructure(rootPath: string, files: Array<{ path: string; relativePath: string }>): Promise<ProjectStructure> {
    const rootFiles = files.filter(f => !f.relativePath.includes('/') && !f.relativePath.includes('\\'))
      .map(f => f.relativePath);

    // Analyze directories
    const directories = new Map<string, DirectoryInfo>();
    
    for (const file of files) {
      const parts = file.relativePath.split(/[/\\]/);
      if (parts.length > 1) {
        const dirName = parts[0];
        if (!directories.has(dirName)) {
          directories.set(dirName, {
            name: dirName,
            path: join(rootPath, dirName),
            purpose: this.inferDirectoryPurpose(dirName),
            fileCount: 0,
            subdirectories: []
          });
        }
        directories.get(dirName)!.fileCount++;
      }
    }

    // Find entry points
    const entryPoints = this.findEntryPoints(files);
    
    // Categorize files
    const configFiles = files.filter(f => this.isConfigFile(f.relativePath)).map(f => f.relativePath);
    const testFiles = files.filter(f => this.isTestFile(f.relativePath)).map(f => f.relativePath);
    const documentationFiles = files.filter(f => this.isDocumentationFile(f.relativePath)).map(f => f.relativePath);

    return {
      rootFiles,
      directories: Array.from(directories.values()),
      entryPoints,
      configFiles,
      testFiles,
      documentationFiles
    };
  }

  private async analyzeDependencies(rootPath: string): Promise<DependencyInfo> {
    // Try different package managers
    const packageJsonPath = join(rootPath, 'package.json');
    const requirementsPath = join(rootPath, 'requirements.txt');
    const cargoTomlPath = join(rootPath, 'Cargo.toml');

    try {
      // Node.js dependencies
      const packageJson = await this.readJsonFile(packageJsonPath);
      if (packageJson) {
        const dependencies = [
          ...Object.entries(packageJson.dependencies || {}).map(([name, version]) => ({
            name,
            version: version as string,
            type: 'production' as const
          })),
          ...Object.entries(packageJson.devDependencies || {}).map(([name, version]) => ({
            name,
            version: version as string,
            type: 'development' as const
          }))
        ];

        return {
          packageManager: 'npm',
          dependencies,
          scripts: Object.entries(packageJson.scripts || {}).map(([name, command]) => ({
            name,
            command: command as string
          }))
        } as any;
      }
    } catch (error) {
      // Package.json doesn't exist or can't be read
    }

    // Default empty dependency info
    return {
      packageManager: 'unknown',
      dependencies: []
    };
  }

  private async analyzeBuildSystem(rootPath: string): Promise<BuildSystemInfo> {
    const configFiles: string[] = [];
    const scripts: Array<{ name: string; command: string }> = [];

    // Check for various build systems
    const buildFiles = [
      'package.json', 'webpack.config.js', 'vite.config.js', 'rollup.config.js',
      'Makefile', 'CMakeLists.txt', 'build.gradle', 'pom.xml', 'Cargo.toml'
    ];

    for (const file of buildFiles) {
      try {
        await stat(join(rootPath, file));
        configFiles.push(file);
      } catch (error) {
        // File doesn't exist
      }
    }

    // Determine build system type
    let type = 'unknown';
    if (configFiles.includes('package.json')) {
      if (configFiles.includes('webpack.config.js')) type = 'webpack';
      else if (configFiles.includes('vite.config.js')) type = 'vite';
      else if (configFiles.includes('rollup.config.js')) type = 'rollup';
      else type = 'npm';
    } else if (configFiles.includes('Makefile')) {
      type = 'make';
    } else if (configFiles.includes('CMakeLists.txt')) {
      type = 'cmake';
    } else if (configFiles.includes('build.gradle')) {
      type = 'gradle';
    } else if (configFiles.includes('pom.xml')) {
      type = 'maven';
    } else if (configFiles.includes('Cargo.toml')) {
      type = 'cargo';
    }

    return {
      type,
      configFiles,
      scripts
    };
  }

  private async analyzeDocumentation(rootPath: string, files: Array<{ path: string; relativePath: string }>): Promise<DocumentationInfo> {
    const docFiles = files.filter(f => this.isDocumentationFile(f.relativePath));
    
    const hasReadme = docFiles.some(f => f.relativePath.toLowerCase().startsWith('readme'));
    const hasChangelog = docFiles.some(f => f.relativePath.toLowerCase().includes('changelog'));
    const hasContributing = docFiles.some(f => f.relativePath.toLowerCase().includes('contributing'));
    const hasLicense = docFiles.some(f => f.relativePath.toLowerCase().includes('license'));

    // Analyze README quality if it exists
    let readmeQuality: DocumentationInfo['readmeQuality'] = 'poor';
    if (hasReadme) {
      readmeQuality = await this.analyzeReadmeQuality(rootPath);
    }

    // Count code comments
    let codeComments = 0;
    for (const file of files.slice(0, 50)) { // Sample first 50 files
      try {
        const content = await readFile(file.path, 'utf-8');
        codeComments += this.countComments(content, extname(file.path));
      } catch (error) {
        // Skip files that can't be read
      }
    }

    return {
      hasReadme,
      readmeQuality,
      hasChangelog,
      hasContributing,
      hasLicense,
      apiDocumentation: docFiles.some(f => f.relativePath.toLowerCase().includes('api')),
      codeComments
    };
  }

  private async calculateCodeMetrics(files: Array<{ path: string; relativePath: string }>): Promise<CodeMetrics> {
    let totalFiles = 0;
    let totalLines = 0;
    let codeLines = 0;
    let commentLines = 0;
    let blankLines = 0;

    for (const file of files) {
      try {
        const content = await readFile(file.path, 'utf-8');
        const lines = content.split('\n');
        
        totalFiles++;
        totalLines += lines.length;

        for (const line of lines) {
          const trimmed = line.trim();
          if (trimmed === '') {
            blankLines++;
          } else if (this.isCommentLine(trimmed, extname(file.path))) {
            commentLines++;
          } else {
            codeLines++;
          }
        }
      } catch (error) {
        // Skip files that can't be read
      }
    }

    return {
      totalFiles,
      totalLines,
      codeLines,
      commentLines,
      blankLines,
      complexity: {
        average: 0, // Would need more sophisticated analysis
        highest: 0,
        highestFile: ''
      },
      duplication: {
        percentage: 0, // Would need duplication detection
        duplicatedLines: 0
      }
    };
  }

  private generateRecommendations(analysis: Partial<ProjectAnalysis>): string[] {
    const recommendations: string[] = [];

    // Documentation recommendations
    if (analysis.documentation) {
      if (!analysis.documentation.hasReadme) {
        recommendations.push('Add a README.md file to document your project');
      } else if (analysis.documentation.readmeQuality === 'poor') {
        recommendations.push('Improve README.md with better documentation');
      }

      if (!analysis.documentation.hasLicense) {
        recommendations.push('Add a LICENSE file to clarify usage rights');
      }

      if (!analysis.documentation.hasChangelog) {
        recommendations.push('Consider adding a CHANGELOG.md to track changes');
      }
    }

    // Code quality recommendations
    if (analysis.codeMetrics) {
      const commentRatio = analysis.codeMetrics.commentLines / analysis.codeMetrics.codeLines;
      if (commentRatio < 0.1) {
        recommendations.push('Consider adding more code comments for better maintainability');
      }
    }

    // Build system recommendations
    if (analysis.buildSystem?.type === 'unknown') {
      recommendations.push('Consider setting up a proper build system');
    }

    return recommendations;
  }

  // Helper methods
  private getLanguageFromExtension(ext: string): string | null {
    const extMap: Record<string, string> = {
      '.js': 'JavaScript',
      '.jsx': 'JavaScript',
      '.ts': 'TypeScript',
      '.tsx': 'TypeScript',
      '.py': 'Python',
      '.java': 'Java',
      '.cs': 'C#',
      '.go': 'Go',
      '.rs': 'Rust',
      '.php': 'PHP',
      '.rb': 'Ruby',
      '.cpp': 'C++',
      '.c': 'C',
      '.h': 'C/C++',
      '.swift': 'Swift',
      '.kt': 'Kotlin'
    };
    return extMap[ext] || null;
  }

  private async detectFrameworks(language: string, files: Array<{ path: string; relativePath: string }>): Promise<string[]> {
    const frameworks: string[] = [];
    
    // Sample some files to detect frameworks
    const sampleFiles = files.filter(f => this.getLanguageFromExtension(extname(f.path)) === language).slice(0, 10);
    
    for (const file of sampleFiles) {
      try {
        const content = await readFile(file.path, 'utf-8');
        
        for (const [framework, patterns] of this.frameworkDetectors) {
          if (patterns.some(pattern => pattern.test(content))) {
            if (!frameworks.includes(framework)) {
              frameworks.push(framework);
            }
          }
        }
      } catch (error) {
        // Skip files that can't be read
      }
    }
    
    return frameworks;
  }

  private inferDirectoryPurpose(dirName: string): string {
    const purposeMap: Record<string, string> = {
      'src': 'Source code',
      'lib': 'Library code',
      'test': 'Test files',
      'tests': 'Test files',
      'spec': 'Test specifications',
      'docs': 'Documentation',
      'doc': 'Documentation',
      'build': 'Build output',
      'dist': 'Distribution files',
      'public': 'Public assets',
      'assets': 'Static assets',
      'static': 'Static files',
      'config': 'Configuration files',
      'scripts': 'Build/utility scripts',
      'tools': 'Development tools',
      'examples': 'Example code',
      'demo': 'Demo applications'
    };
    
    return purposeMap[dirName.toLowerCase()] || 'Unknown purpose';
  }

  private findEntryPoints(files: Array<{ path: string; relativePath: string }>): string[] {
    const entryPoints: string[] = [];
    
    const commonEntryPoints = [
      'index.js', 'index.ts', 'main.js', 'main.ts', 'app.js', 'app.ts',
      'server.js', 'server.ts', 'index.html', 'main.py', '__main__.py',
      'Main.java', 'Program.cs', 'main.go', 'main.rs'
    ];
    
    for (const file of files) {
      const fileName = basename(file.relativePath);
      if (commonEntryPoints.includes(fileName)) {
        entryPoints.push(file.relativePath);
      }
    }
    
    return entryPoints;
  }

  private isConfigFile(filePath: string): boolean {
    const configPatterns = [
      /\.config\./,
      /\.conf$/,
      /\.ini$/,
      /\.env$/,
      /\.json$/,
      /\.yaml$/,
      /\.yml$/,
      /\.toml$/,
      /package\.json$/,
      /tsconfig\.json$/,
      /webpack\.config\./,
      /babel\.config\./,
      /eslint/,
      /prettier/
    ];
    
    return configPatterns.some(pattern => pattern.test(filePath.toLowerCase()));
  }

  private isTestFile(filePath: string): boolean {
    const testPatterns = [
      /\.test\./,
      /\.spec\./,
      /test/,
      /tests/,
      /__tests__/
    ];
    
    return testPatterns.some(pattern => pattern.test(filePath.toLowerCase()));
  }

  private isDocumentationFile(filePath: string): boolean {
    const docPatterns = [
      /readme/i,
      /changelog/i,
      /contributing/i,
      /license/i,
      /\.md$/,
      /\.txt$/,
      /docs?\//,
      /documentation/i
    ];
    
    return docPatterns.some(pattern => pattern.test(filePath));
  }

  private async analyzeReadmeQuality(rootPath: string): Promise<DocumentationInfo['readmeQuality']> {
    try {
      const readmeFiles = ['README.md', 'readme.md', 'README.txt', 'readme.txt'];
      let content = '';
      
      for (const file of readmeFiles) {
        try {
          content = await readFile(join(rootPath, file), 'utf-8');
          break;
        } catch (error) {
          // Try next file
        }
      }
      
      if (!content) return 'poor';
      
      const lines = content.split('\n').filter(line => line.trim());
      const wordCount = content.split(/\s+/).length;
      
      // Simple quality heuristics
      if (wordCount < 50) return 'poor';
      if (wordCount < 200) return 'basic';
      if (wordCount < 500) return 'good';
      return 'excellent';
    } catch (error) {
      return 'poor';
    }
  }

  private countComments(content: string, extension: string): number {
    const lines = content.split('\n');
    let commentCount = 0;
    
    for (const line of lines) {
      if (this.isCommentLine(line.trim(), extension)) {
        commentCount++;
      }
    }
    
    return commentCount;
  }

  private isCommentLine(line: string, extension: string): boolean {
    if (!line) return false;
    
    const commentPatterns: Record<string, RegExp[]> = {
      '.js': [/^\/\//, /^\/\*/, /^\*/],
      '.ts': [/^\/\//, /^\/\*/, /^\*/],
      '.py': [/^#/],
      '.java': [/^\/\//, /^\/\*/, /^\*/],
      '.cs': [/^\/\//, /^\/\*/, /^\*/],
      '.go': [/^\/\//, /^\/\*/, /^\*/],
      '.rs': [/^\/\//, /^\/\*/, /^\*/],
      '.php': [/^\/\//, /^\/\*/, /^\*/, /^#/],
      '.rb': [/^#/]
    };
    
    const patterns = commentPatterns[extension] || [];
    return patterns.some(pattern => pattern.test(line));
  }

  private async readJsonFile(filePath: string): Promise<any> {
    try {
      const content = await readFile(filePath, 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      return null;
    }
  }
}

// Global project analyzer instance
let globalProjectAnalyzer: ProjectAnalyzer | null = null;

export function getProjectAnalyzer(): ProjectAnalyzer {
  if (!globalProjectAnalyzer) {
    globalProjectAnalyzer = new ProjectAnalyzer();
  }
  return globalProjectAnalyzer;
}

// Convenience function
export async function analyzeProject(rootPath: string): Promise<ProjectAnalysis> {
  const analyzer = getProjectAnalyzer();
  return analyzer.analyzeProject(rootPath);
}
