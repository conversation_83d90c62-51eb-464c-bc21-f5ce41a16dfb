// Token counting and limit management
import { ChatMessage } from './client.js';
import { getModelConfig, ModelConfig } from '../config/models.js';
import { logger } from './logger.js';

export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
}

export interface TokenLimitCheck {
  withinLimits: boolean;
  currentTokens: number;
  maxTokens: number;
  utilizationPercentage: number;
  recommendedAction?: 'compress' | 'truncate' | 'split';
}

export interface CompressionOptions {
  strategy: 'truncate' | 'summarize' | 'sliding_window';
  preserveRecent: number;
  maxTokens: number;
}

export class TokenLimitManager {
  private readonly SAFETY_MARGIN = 0.1; // 10% safety margin
  private readonly WARNING_THRESHOLD = 0.8; // 80% utilization warning

  public async checkTokenLimits(
    messages: ChatMessage[],
    model?: string,
    reservedTokens: number = 1000
  ): Promise<TokenLimitCheck> {
    const modelConfig = model ? getModelConfig(model) : this.getDefaultModelConfig();
    if (!modelConfig) {
      throw new Error(`Model configuration not found for: ${model}`);
    }

    const currentTokens = await this.countTokens(messages);
    const maxTokens = modelConfig.maxTokens - reservedTokens;
    const utilizationPercentage = currentTokens / maxTokens;

    const withinLimits = currentTokens <= maxTokens * (1 - this.SAFETY_MARGIN);

    let recommendedAction: 'compress' | 'truncate' | 'split' | undefined;
    if (!withinLimits) {
      if (utilizationPercentage > 0.95) {
        recommendedAction = 'truncate';
      } else if (utilizationPercentage > 0.85) {
        recommendedAction = 'compress';
      } else {
        recommendedAction = 'split';
      }
    }

    const result: TokenLimitCheck = {
      withinLimits,
      currentTokens,
      maxTokens,
      utilizationPercentage,
      recommendedAction
    };

    if (utilizationPercentage > this.WARNING_THRESHOLD) {
      logger.warn('Token usage approaching limits', {
        currentTokens,
        maxTokens,
        utilizationPercentage: Math.round(utilizationPercentage * 100),
        recommendedAction
      });
    }

    return result;
  }

  public async countTokens(messages: ChatMessage[]): Promise<number> {
    // Simplified token counting - in production, use proper tokenizer
    let totalTokens = 0;

    for (const message of messages) {
      // Rough estimation: 1 token ≈ 4 characters for English text
      const contentTokens = Math.ceil(message.content.length / 4);
      
      // Add overhead for message structure
      const messageOverhead = 10; // role, formatting, etc.
      
      totalTokens += contentTokens + messageOverhead;

      // Add tokens for tool calls if present
      if (message.toolCalls) {
        for (const toolCall of message.toolCalls) {
          const toolTokens = Math.ceil(JSON.stringify(toolCall).length / 4);
          totalTokens += toolTokens;
        }
      }

      // Add tokens for tool results if present
      if (message.toolResults) {
        for (const toolResult of message.toolResults) {
          const resultTokens = Math.ceil(JSON.stringify(toolResult).length / 4);
          totalTokens += resultTokens;
        }
      }
    }

    return totalTokens;
  }

  public async compressMessages(
    messages: ChatMessage[],
    options: CompressionOptions
  ): Promise<ChatMessage[]> {
    logger.info('Compressing messages', {
      originalCount: messages.length,
      strategy: options.strategy,
      maxTokens: options.maxTokens
    });

    switch (options.strategy) {
      case 'truncate':
        return this.truncateMessages(messages, options);
      
      case 'summarize':
        return this.summarizeMessages(messages, options);
      
      case 'sliding_window':
        return this.slidingWindowMessages(messages, options);
      
      default:
        throw new Error(`Unknown compression strategy: ${options.strategy}`);
    }
  }

  private async truncateMessages(
    messages: ChatMessage[],
    options: CompressionOptions
  ): Promise<ChatMessage[]> {
    // Keep system message (if any) and recent messages
    const systemMessages = messages.filter(m => m.role === 'system');
    const nonSystemMessages = messages.filter(m => m.role !== 'system');
    
    // Keep the most recent messages
    const recentMessages = nonSystemMessages.slice(-options.preserveRecent);
    
    const result = [...systemMessages, ...recentMessages];
    
    logger.debug('Messages truncated', {
      originalCount: messages.length,
      resultCount: result.length,
      preservedRecent: options.preserveRecent
    });

    return result;
  }

  private async summarizeMessages(
    messages: ChatMessage[],
    options: CompressionOptions
  ): Promise<ChatMessage[]> {
    // Keep system message and recent messages, summarize the middle
    const systemMessages = messages.filter(m => m.role === 'system');
    const nonSystemMessages = messages.filter(m => m.role !== 'system');
    
    if (nonSystemMessages.length <= options.preserveRecent) {
      return messages;
    }

    const recentMessages = nonSystemMessages.slice(-options.preserveRecent);
    const messagesToSummarize = nonSystemMessages.slice(0, -options.preserveRecent);

    // Create a summary of the messages to compress
    const summaryContent = this.createMessageSummary(messagesToSummarize);
    const summaryMessage: ChatMessage = {
      role: 'system',
      content: `[Previous conversation summary: ${summaryContent}]`
    };

    const result = [...systemMessages, summaryMessage, ...recentMessages];
    
    logger.debug('Messages summarized', {
      originalCount: messages.length,
      resultCount: result.length,
      summarizedCount: messagesToSummarize.length
    });

    return result;
  }

  private async slidingWindowMessages(
    messages: ChatMessage[],
    options: CompressionOptions
  ): Promise<ChatMessage[]> {
    // Keep system messages and implement sliding window for conversation
    const systemMessages = messages.filter(m => m.role === 'system');
    const conversationMessages = messages.filter(m => m.role !== 'system');
    
    // Calculate how many conversation messages we can keep
    const systemTokens = await this.countTokens(systemMessages);
    const availableTokens = options.maxTokens - systemTokens;
    
    let currentTokens = 0;
    const keptMessages: ChatMessage[] = [];
    
    // Start from the end and work backwards
    for (let i = conversationMessages.length - 1; i >= 0; i--) {
      const message = conversationMessages[i];
      const messageTokens = await this.countTokens([message]);
      
      if (currentTokens + messageTokens <= availableTokens) {
        keptMessages.unshift(message);
        currentTokens += messageTokens;
      } else {
        break;
      }
    }

    const result = [...systemMessages, ...keptMessages];
    
    logger.debug('Sliding window applied', {
      originalCount: messages.length,
      resultCount: result.length,
      tokensUsed: currentTokens,
      availableTokens
    });

    return result;
  }

  private createMessageSummary(messages: ChatMessage[]): string {
    const userMessages = messages.filter(m => m.role === 'user');
    const assistantMessages = messages.filter(m => m.role === 'assistant');
    
    const topics = this.extractTopics(messages);
    const keyPoints = this.extractKeyPoints(messages);

    return `Discussed ${topics.join(', ')}. Key points: ${keyPoints.join('; ')}.`;
  }

  private extractTopics(messages: ChatMessage[]): string[] {
    // Simple topic extraction - in production, use NLP
    const topics = new Set<string>();
    
    for (const message of messages) {
      const words = message.content.toLowerCase().split(/\s+/);
      
      // Look for common programming/technical terms
      const techTerms = words.filter(word => 
        word.length > 4 && 
        (word.includes('function') || word.includes('class') || 
         word.includes('method') || word.includes('variable') ||
         word.includes('error') || word.includes('debug') ||
         word.includes('code') || word.includes('file'))
      );
      
      techTerms.forEach(term => topics.add(term));
    }

    return Array.from(topics).slice(0, 5); // Limit to 5 topics
  }

  private extractKeyPoints(messages: ChatMessage[]): string[] {
    // Extract key points from assistant messages
    const keyPoints: string[] = [];
    
    for (const message of messages) {
      if (message.role === 'assistant') {
        const sentences = message.content.split(/[.!?]+/);
        const importantSentences = sentences.filter(s => 
          s.length > 20 && s.length < 100 &&
          (s.includes('should') || s.includes('need') || s.includes('important'))
        );
        
        keyPoints.push(...importantSentences.slice(0, 2));
      }
    }

    return keyPoints.slice(0, 3); // Limit to 3 key points
  }

  private getDefaultModelConfig(): ModelConfig | undefined {
    // Return a default configuration if no specific model is provided
    return {
      id: 'default',
      name: 'default',
      provider: 'openai',
      maxTokens: 4096,
      supportsTools: true,
      supportsStreaming: true,
      costPer1kTokens: { input: 0.001, output: 0.002 }
    };
  }

  public estimateTokenCost(usage: TokenUsage, model?: string): number {
    const modelConfig = model ? getModelConfig(model) : this.getDefaultModelConfig();
    if (!modelConfig?.costPer1kTokens) {
      return 0;
    }

    const inputCost = (usage.promptTokens / 1000) * modelConfig.costPer1kTokens.input;
    const outputCost = (usage.completionTokens / 1000) * modelConfig.costPer1kTokens.output;
    
    return inputCost + outputCost;
  }

  public getOptimalBatchSize(model?: string): number {
    const modelConfig = model ? getModelConfig(model) : this.getDefaultModelConfig();
    if (!modelConfig) {
      return 1;
    }

    // Calculate optimal batch size based on token limits
    const maxTokens = modelConfig.maxTokens;
    
    if (maxTokens >= 32000) {
      return 10; // Large context models can handle bigger batches
    } else if (maxTokens >= 8000) {
      return 5;
    } else {
      return 2;
    }
  }
}
