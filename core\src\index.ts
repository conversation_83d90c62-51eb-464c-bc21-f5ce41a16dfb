// Core exports for Arien CLI
export * from './core/client.js';
export * from './core/logger.js';
// export * from './core/ArienChat.js'; // Temporarily disabled due to missing dependencies
// export * from './core/ArienRequest.js'; // Temporarily disabled
// export * from './core/prompts.js'; // Temporarily disabled
// export * from './core/tokenLimits.js'; // Temporarily disabled
// export * from './core/turn.js'; // Temporarily disabled
// export * from './core/coreToolScheduler.js'; // Temporarily disabled
// export * from './core/contentGenerator.js'; // Temporarily disabled
// export * from './core/modelCheck.js'; // Temporarily disabled
// export * from './core/nonInteractiveToolExecutor.js'; // Temporarily disabled

// Configuration exports
export * from './config/config.js';
export * from './config/models.js';
// export * from './config/auth.js'; // Temporarily disabled
// export * from './config/credentialStore.js'; // Temporarily disabled
// export { ProviderManager } from './config/providerManager.js'; // Temporarily disabled
// export * from './config/extension.js'; // Temporarily disabled
// export * from './config/sandboxConfig.js'; // Temporarily disabled
// export * from './config/settings.js'; // Temporarily disabled

// Tool exports - minimal set for now (avoiding conflicts)
export {
  ToolResult,
  ToolContext,
  ToolDefinition,
  BaseTool,
  FilePathSchema,
  FileContentSchema,
  ShellCommandSchema,
  WebFetchSchema,
  ToolExecutionError as ToolsExecutionError,
  ToolValidationError,
  ToolApprovalError,
  toolStatsCollector
} from './tools/tools.js';
export * from './tools/tool-registry.js';
// Temporarily disable problematic tools
// export * from './tools/edit.js';
// export * from './tools/read-file.js';
// export * from './tools/write-file.js';
// export * from './tools/shell.js';
// export { WebFetchTool } from './tools/web-fetch.js';
// export { WebSearchTool } from './tools/web-search.js';
// export * from './tools/memoryTool.js';
// export * from './tools/ls.js';
// export * from './tools/grep.js';
// export * from './tools/glob.js';
// export * from './tools/mcp-client.js';
// export * from './tools/mcp-tool.js';
// export * from './tools/diffOptions.js';
// export * from './tools/read-many-files.js';
// export * from './tools/modifiable-tool.js';

// Service exports - temporarily disabled
// export * from './services/fileDiscoveryService.js';
// export * from './services/gitService.js';
// export * from './services/apiService.js';

// Utility exports - minimal set with explicit exports to avoid conflicts
export * from './utils/errors.js';
// export * from './utils/fileUtils.js'; // Temporarily disabled

// Security exports
export * from './security/index.js';
// export * from './utils/gitUtils.js'; // File missing
export { getRelativePath, getFileExtension } from './utils/fileUtils.js'; // Avoid conflicts with paths.js

// Retry utilities (avoiding conflicts)
export {
  RetryConfig,
  RetryOptions,
  withRetry,
  ExponentialBackoff,
  RateLimiter as RetryRateLimiter
} from './utils/retry.js';

export * from './utils/session.js';
export * from './utils/user_id.js';
export * from './utils/LruCache.js';
export * from './utils/editCorrector.js';
export * from './utils/editor.js';

// Error reporting (avoiding conflicts)
export {
  ErrorReport,
  ErrorReporter,
  errorReporter,
  setupGlobalErrorHandlers,
  formatErrorForUser as formatErrorForUserReport
} from './utils/errorReporting.js';

// Fetch utilities (avoiding conflicts)
export {
  FetchOptions,
  FetchResult,
  HttpClient,
  httpClient,
  get,
  post,
  put,
  patch,
  del,
  isRetryableError as isRetryableHttpError,
  createRetryCondition,
  RateLimiter as FetchRateLimiter,
  createRateLimitedClient
} from './utils/fetch.js';
// export * from './utils/generateContentResponseUtilities.js'; // File missing
// export * from './utils/getFolderStructure.js'; // File missing
export * from './utils/gitIgnoreParser.js';
// export * from './utils/memoryDiscovery.js'; // File missing
// export * from './utils/messageInspectors.js'; // File missing
// export * from './utils/nextSpeakerChecker.js'; // File missing
// export * from './utils/schemaValidator.js'; // File missing
export * from './utils/bfsFileSearch.js';

// Telemetry exports
export * from './telemetry/index.js';

// Code assist exports
export * from './code_assist/codeAssist.js';
export * from './code_assist/types.js';
