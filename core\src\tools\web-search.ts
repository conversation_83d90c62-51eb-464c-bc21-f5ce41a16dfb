// Web search integration tool
import { z } from 'zod';
import fetch from 'node-fetch';
import { <PERSON>Tool, ToolResult, ToolContext, ToolExecutionError } from './tools.js';
import { logger } from '../core/logger.js';

export const WebSearchSchema = z.object({
  query: z.string().describe('Search query'),
  maxResults: z.number().optional().default(10).describe('Maximum number of results to return'),
  searchEngine: z.enum(['google', 'bing', 'duckduckgo']).optional().default('duckduckgo').describe('Search engine to use'),
  safeSearch: z.boolean().optional().default(true).describe('Enable safe search filtering'),
  region: z.string().optional().describe('Region/country code for localized results (e.g., "us", "uk")'),
  language: z.string().optional().describe('Language code for results (e.g., "en", "es")'),
  timeRange: z.enum(['day', 'week', 'month', 'year', 'all']).optional().default('all').describe('Time range for results'),
  includeSnippets: z.boolean().optional().default(true).describe('Include content snippets in results'),
  timeout: z.number().optional().default(30000).describe('Request timeout in milliseconds')
});

export interface SearchResult {
  title: string;
  url: string;
  snippet?: string;
  displayUrl?: string;
  rank: number;
  source?: string;
}

export interface WebSearchResult {
  query: string;
  results: SearchResult[];
  totalResults: number;
  searchTime: number;
  searchEngine: string;
  suggestions?: string[];
}

export class WebSearchTool extends BaseTool {
  private readonly API_ENDPOINTS = {
    duckduckgo: 'https://api.duckduckgo.com/',
    google: 'https://www.googleapis.com/customsearch/v1',
    bing: 'https://api.bing.microsoft.com/v7.0/search'
  };

  constructor() {
    super({
      name: 'web-search',
      description: 'Search the web using various search engines',
      parameters: WebSearchSchema,
      requiresApproval: false,
      riskLevel: 'low',
      category: 'web'
    });
  }

  public async execute(params: any, context: ToolContext): Promise<ToolResult> {
    try {
      const validatedParams = this.validateParams(params);
      const startTime = Date.now();

      logger.debug('Performing web search', {
        query: validatedParams.query,
        searchEngine: validatedParams.searchEngine,
        maxResults: validatedParams.maxResults
      });

      const result = await this.performSearch(validatedParams);
      result.searchTime = Date.now() - startTime;

      logger.info('Web search completed', {
        query: validatedParams.query,
        searchEngine: validatedParams.searchEngine,
        totalResults: result.totalResults,
        searchTime: result.searchTime
      });

      const summary = this.createSummary(result);
      return this.createSuccessResult(summary, result);

    } catch (error) {
      logger.error('Web search failed', {
        error: error instanceof Error ? error.message : String(error),
        params: this.sanitizeParams(params)
      });

      if (error instanceof ToolExecutionError) {
        throw error;
      }

      throw new ToolExecutionError(
        this.definition.name,
        error instanceof Error ? error.message : String(error),
        error instanceof Error ? error : undefined
      );
    }
  }

  private async performSearch(params: any): Promise<WebSearchResult> {
    switch (params.searchEngine) {
      case 'duckduckgo':
        return this.searchDuckDuckGo(params);
      case 'google':
        return this.searchGoogle(params);
      case 'bing':
        return this.searchBing(params);
      default:
        throw new ToolExecutionError(
          this.definition.name,
          `Unsupported search engine: ${params.searchEngine}`
        );
    }
  }

  private async searchDuckDuckGo(params: any): Promise<WebSearchResult> {
    // DuckDuckGo Instant Answer API (limited but free)
    const url = new URL(this.API_ENDPOINTS.duckduckgo);
    url.searchParams.set('q', params.query);
    url.searchParams.set('format', 'json');
    url.searchParams.set('no_html', '1');
    url.searchParams.set('skip_disambig', '1');

    if (params.safeSearch) {
      url.searchParams.set('safe_search', 'strict');
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), params.timeout);

    try {
        const response = await fetch(url.toString(), {
          signal: controller.signal,
          headers: {
            'User-Agent': 'Arien-CLI/1.0'
          }
        });
        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json() as any;

        // DuckDuckGo API returns different types of results
        const results: SearchResult[] = [];
        let rank = 1;

        // Add instant answer if available
        if (data.Answer) {
          results.push({
            title: 'Instant Answer',
            url: data.AnswerURL || '#',
            snippet: data.Answer,
            rank: rank++,
            source: 'instant'
          });
        }

        // Add abstract if available
        if (data.Abstract) {
          results.push({
            title: data.Heading || 'Abstract',
            url: data.AbstractURL || '#',
            snippet: data.Abstract,
            rank: rank++,
            source: 'abstract'
          });
        }

        // Add related topics
        if (data.RelatedTopics && Array.isArray(data.RelatedTopics)) {
          for (const topic of data.RelatedTopics.slice(0, params.maxResults - results.length)) {
            if (topic.Text && topic.FirstURL) {
              results.push({
                title: topic.Text.split(' - ')[0] || topic.Text,
                url: topic.FirstURL,
                snippet: params.includeSnippets ? topic.Text : undefined,
                rank: rank++,
                source: 'related'
              });
            }
          }
        }

        return {
          query: params.query,
          results: results.slice(0, params.maxResults),
          totalResults: results.length,
          searchTime: 0,
          searchEngine: 'duckduckgo'
        };
      } catch (error) {
        clearTimeout(timeoutId);
        if (error instanceof Error && error.name === 'AbortError') {
          throw new Error(`Request timeout after ${params.timeout}ms`);
        }
        throw error;
      }
  }

  private async searchGoogle(params: any): Promise<WebSearchResult> {
    const apiKey = process.env.GOOGLE_SEARCH_API_KEY;
    const searchEngineId = process.env.GOOGLE_SEARCH_ENGINE_ID;

    if (!apiKey || !searchEngineId) {
      throw new ToolExecutionError(
        this.definition.name,
        'Google search requires GOOGLE_SEARCH_API_KEY and GOOGLE_SEARCH_ENGINE_ID environment variables'
      );
    }

    try {
      const url = new URL(this.API_ENDPOINTS.google);
      url.searchParams.set('key', apiKey);
      url.searchParams.set('cx', searchEngineId);
      url.searchParams.set('q', params.query);
      url.searchParams.set('num', Math.min(params.maxResults, 10).toString());

      if (params.safeSearch) {
        url.searchParams.set('safe', 'active');
      }

      if (params.region) {
        url.searchParams.set('gl', params.region);
      }

      if (params.language) {
        url.searchParams.set('lr', `lang_${params.language}`);
      }

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), params.timeout);

      try {
        const response = await fetch(url.toString(), {
          signal: controller.signal
        });
        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json() as any;

        const results: SearchResult[] = [];

        if (data.items && Array.isArray(data.items)) {
          data.items.forEach((item: any, index: number) => {
            results.push({
              title: item.title,
              url: item.link,
              snippet: params.includeSnippets ? item.snippet : undefined,
              displayUrl: item.displayLink,
              rank: index + 1,
              source: 'web'
            });
          });
        }

        return {
          query: params.query,
          results,
          totalResults: parseInt(data.searchInformation?.totalResults || '0'),
          searchTime: 0,
          searchEngine: 'google',
          suggestions: data.spelling?.correctedQuery ? [data.spelling.correctedQuery] : undefined
        };
      } catch (fetchError) {
        clearTimeout(timeoutId);
        if (fetchError instanceof Error && fetchError.name === 'AbortError') {
          throw new Error(`Request timeout after ${params.timeout}ms`);
        }
        throw fetchError;
      }

    } catch (error) {
      throw new ToolExecutionError(
        this.definition.name,
        `Google search failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private async searchBing(params: any): Promise<WebSearchResult> {
    const apiKey = process.env.BING_SEARCH_API_KEY;

    if (!apiKey) {
      throw new ToolExecutionError(
        this.definition.name,
        'Bing search requires BING_SEARCH_API_KEY environment variable'
      );
    }

    try {
      const url = new URL(this.API_ENDPOINTS.bing);
      url.searchParams.set('q', params.query);
      url.searchParams.set('count', Math.min(params.maxResults, 50).toString());

      if (params.safeSearch) {
        url.searchParams.set('safeSearch', 'Strict');
      }

      if (params.region) {
        url.searchParams.set('mkt', params.region);
      }

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), params.timeout);

      try {
        const response = await fetch(url.toString(), {
          signal: controller.signal,
          headers: {
            'Ocp-Apim-Subscription-Key': apiKey
          }
        });
        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json() as any;
        const results: SearchResult[] = [];

        if (data.webPages && data.webPages.value && Array.isArray(data.webPages.value)) {
          data.webPages.value.forEach((item: any, index: number) => {
            results.push({
              title: item.name,
              url: item.url,
              snippet: params.includeSnippets ? item.snippet : undefined,
              displayUrl: item.displayUrl,
              rank: index + 1,
              source: 'web'
            });
          });
        }

        return {
          query: params.query,
          results,
          totalResults: data.webPages?.totalEstimatedMatches || results.length,
          searchTime: 0,
          searchEngine: 'bing'
        };
      } catch (fetchError) {
        clearTimeout(timeoutId);
        if (fetchError instanceof Error && fetchError.name === 'AbortError') {
          throw new Error(`Request timeout after ${params.timeout}ms`);
        }
        throw fetchError;
      }

    } catch (error) {
      throw new ToolExecutionError(
        this.definition.name,
        `Bing search failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private createSummary(result: WebSearchResult): string {
    const lines = [
      `Search query: "${result.query}"`,
      `Search engine: ${result.searchEngine}`,
      `Found ${result.totalResults} results in ${result.searchTime}ms`,
      ''
    ];

    result.results.forEach((item, index) => {
      lines.push(`${index + 1}. ${item.title}`);
      lines.push(`   ${item.url}`);
      if (item.snippet) {
        lines.push(`   ${item.snippet.substring(0, 150)}${item.snippet.length > 150 ? '...' : ''}`);
      }
      lines.push('');
    });

    if (result.suggestions && result.suggestions.length > 0) {
      lines.push('Suggestions:');
      result.suggestions.forEach(suggestion => {
        lines.push(`  - ${suggestion}`);
      });
    }

    return lines.join('\n');
  }

  private sanitizeParams(params: any): any {
    // Remove potentially sensitive information for logging
    const sanitized = { ...params };
    
    // Limit query length for logging
    if (sanitized.query && sanitized.query.length > 100) {
      sanitized.query = sanitized.query.substring(0, 100) + '...';
    }
    
    return sanitized;
  }
}
