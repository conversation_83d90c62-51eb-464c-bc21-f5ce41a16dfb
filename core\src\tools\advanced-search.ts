// Advanced search tool with semantic and fuzzy search capabilities
import { readFile, stat } from 'fs/promises';
import { join, extname, relative } from 'path';
import { logger } from '../core/logger.js';
import { BfsFileSearch, searchFiles } from '../utils/bfsFileSearch.js';
import { ArienError, ErrorCode } from '../utils/errors.js';

export interface SearchOptions {
  query: string;
  searchType: 'text' | 'regex' | 'fuzzy' | 'semantic';
  caseSensitive?: boolean;
  wholeWord?: boolean;
  includeFiles?: string[];
  excludeFiles?: string[];
  maxResults?: number;
  contextLines?: number;
  searchInBinary?: boolean;
  fuzzyThreshold?: number; // 0-1, lower = more fuzzy
}

export interface SearchResult {
  filePath: string;
  relativePath: string;
  matches: SearchMatch[];
  totalMatches: number;
  fileSize: number;
  lastModified: Date;
}

export interface SearchMatch {
  lineNumber: number;
  columnStart: number;
  columnEnd: number;
  matchedText: string;
  lineContent: string;
  contextBefore?: string[];
  contextAfter?: string[];
  confidence?: number; // For fuzzy/semantic search
}

export interface SearchSummary {
  totalFiles: number;
  filesWithMatches: number;
  totalMatches: number;
  searchTime: number;
  query: string;
  searchType: string;
}

export class AdvancedSearch {
  private stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'
  ]);

  public async search(
    rootPath: string,
    options: SearchOptions
  ): Promise<{ results: SearchResult[]; summary: SearchSummary }> {
    const startTime = Date.now();
    
    try {
      logger.debug('Starting advanced search', {
        rootPath,
        query: options.query,
        searchType: options.searchType
      });

      // Find files to search
      const files = await this.findSearchableFiles(rootPath, options);
      
      // Search in files
      const results: SearchResult[] = [];
      let totalMatches = 0;

      for (const file of files) {
        try {
          const fileResult = await this.searchInFile(file.path, options);
          if (fileResult && fileResult.matches.length > 0) {
            results.push(fileResult);
            totalMatches += fileResult.totalMatches;
          }

          // Limit results if specified
          if (options.maxResults && results.length >= options.maxResults) {
            break;
          }
        } catch (error) {
          logger.debug('Failed to search in file', { filePath: file.path, error });
        }
      }

      // Sort results by relevance
      results.sort((a, b) => {
        // Sort by total matches first, then by confidence if available
        if (a.totalMatches !== b.totalMatches) {
          return b.totalMatches - a.totalMatches;
        }
        
        const aAvgConfidence = a.matches.reduce((sum, m) => sum + (m.confidence || 1), 0) / a.matches.length;
        const bAvgConfidence = b.matches.reduce((sum, m) => sum + (m.confidence || 1), 0) / b.matches.length;
        
        return bAvgConfidence - aAvgConfidence;
      });

      const summary: SearchSummary = {
        totalFiles: files.length,
        filesWithMatches: results.length,
        totalMatches,
        searchTime: Date.now() - startTime,
        query: options.query,
        searchType: options.searchType
      };

      logger.debug('Search completed', summary);

      return { results, summary };
    } catch (error) {
      logger.error('Search failed', { rootPath, options, error });
      throw new ArienError(
        ErrorCode.TOOL_EXECUTION_ERROR,
        `Search failed: ${error instanceof Error ? error.message : String(error)}`,
        { rootPath, options }
      );
    }
  }

  private async findSearchableFiles(rootPath: string, options: SearchOptions): Promise<Array<{ path: string; relativePath: string }>> {
    const searchOptions = {
      maxResults: 10000,
      includeDirectories: false,
      includeHidden: false,
      extensions: options.includeFiles,
      excludePatterns: options.excludeFiles || ['node_modules/**', '.git/**', 'dist/**', 'build/**']
    };

    const files = await searchFiles(rootPath, searchOptions);
    
    return files.map(file => ({
      path: file.path,
      relativePath: file.relativePath
    }));
  }

  private async searchInFile(filePath: string, options: SearchOptions): Promise<SearchResult | null> {
    try {
      // Check if file is binary
      if (!options.searchInBinary && await this.isBinaryFile(filePath)) {
        return null;
      }

      const content = await readFile(filePath, 'utf-8');
      const lines = content.split('\n');
      const matches: SearchMatch[] = [];

      // Search based on type
      switch (options.searchType) {
        case 'text':
          this.searchText(lines, options, matches);
          break;
        case 'regex':
          this.searchRegex(lines, options, matches);
          break;
        case 'fuzzy':
          this.searchFuzzy(lines, options, matches);
          break;
        case 'semantic':
          this.searchSemantic(lines, options, matches);
          break;
      }

      if (matches.length === 0) {
        return null;
      }

      // Add context if requested
      if (options.contextLines && options.contextLines > 0) {
        this.addContext(lines, matches, options.contextLines);
      }

      const stats = await stat(filePath);
      
      return {
        filePath,
        relativePath: relative(process.cwd(), filePath),
        matches,
        totalMatches: matches.length,
        fileSize: stats.size,
        lastModified: stats.mtime
      };
    } catch (error) {
      logger.debug('Failed to search in file', { filePath, error });
      return null;
    }
  }

  private searchText(lines: string[], options: SearchOptions, matches: SearchMatch[]): void {
    const query = options.caseSensitive ? options.query : options.query.toLowerCase();
    
    lines.forEach((line, lineIndex) => {
      const searchLine = options.caseSensitive ? line : line.toLowerCase();
      
      if (options.wholeWord) {
        const regex = new RegExp(`\\b${this.escapeRegex(query)}\\b`, options.caseSensitive ? 'g' : 'gi');
        let match;
        while ((match = regex.exec(line)) !== null) {
          matches.push({
            lineNumber: lineIndex + 1,
            columnStart: match.index,
            columnEnd: match.index + match[0].length,
            matchedText: match[0],
            lineContent: line,
            confidence: 1.0
          });
        }
      } else {
        let startIndex = 0;
        let index;
        while ((index = searchLine.indexOf(query, startIndex)) !== -1) {
          matches.push({
            lineNumber: lineIndex + 1,
            columnStart: index,
            columnEnd: index + query.length,
            matchedText: line.substring(index, index + query.length),
            lineContent: line,
            confidence: 1.0
          });
          startIndex = index + 1;
        }
      }
    });
  }

  private searchRegex(lines: string[], options: SearchOptions, matches: SearchMatch[]): void {
    try {
      const flags = options.caseSensitive ? 'g' : 'gi';
      const regex = new RegExp(options.query, flags);
      
      lines.forEach((line, lineIndex) => {
        let match;
        while ((match = regex.exec(line)) !== null) {
          matches.push({
            lineNumber: lineIndex + 1,
            columnStart: match.index,
            columnEnd: match.index + match[0].length,
            matchedText: match[0],
            lineContent: line,
            confidence: 1.0
          });
        }
      });
    } catch (error) {
      throw new ArienError(
        ErrorCode.INVALID_INPUT,
        `Invalid regex pattern: ${options.query}`,
        { pattern: options.query, error }
      );
    }
  }

  private searchFuzzy(lines: string[], options: SearchOptions, matches: SearchMatch[]): void {
    const threshold = options.fuzzyThreshold || 0.6;
    const query = options.caseSensitive ? options.query : options.query.toLowerCase();
    
    lines.forEach((line, lineIndex) => {
      const searchLine = options.caseSensitive ? line : line.toLowerCase();
      const words = searchLine.split(/\s+/);
      
      words.forEach((word, wordIndex) => {
        const similarity = this.calculateSimilarity(query, word);
        if (similarity >= threshold) {
          const wordStart = searchLine.indexOf(word, wordIndex > 0 ? searchLine.indexOf(words[wordIndex - 1]) + words[wordIndex - 1].length : 0);
          
          matches.push({
            lineNumber: lineIndex + 1,
            columnStart: wordStart,
            columnEnd: wordStart + word.length,
            matchedText: line.substring(wordStart, wordStart + word.length),
            lineContent: line,
            confidence: similarity
          });
        }
      });
    });
  }

  private searchSemantic(lines: string[], options: SearchOptions, matches: SearchMatch[]): void {
    // Simplified semantic search using keyword matching and context
    const queryWords = this.tokenize(options.query.toLowerCase());
    const queryKeywords = queryWords.filter(word => !this.stopWords.has(word));
    
    lines.forEach((line, lineIndex) => {
      const lineWords = this.tokenize(line.toLowerCase());
      const lineKeywords = lineWords.filter(word => !this.stopWords.has(word));
      
      // Calculate semantic similarity
      const commonKeywords = queryKeywords.filter(word => lineKeywords.includes(word));
      const similarity = commonKeywords.length / Math.max(queryKeywords.length, 1);
      
      if (similarity > 0.3) { // Threshold for semantic match
        // Find the best matching span in the line
        const matchSpan = this.findBestMatchSpan(line, queryKeywords);
        
        matches.push({
          lineNumber: lineIndex + 1,
          columnStart: matchSpan.start,
          columnEnd: matchSpan.end,
          matchedText: line.substring(matchSpan.start, matchSpan.end),
          lineContent: line,
          confidence: similarity
        });
      }
    });
  }

  private addContext(lines: string[], matches: SearchMatch[], contextLines: number): void {
    matches.forEach(match => {
      const lineIndex = match.lineNumber - 1;
      
      // Add context before
      const beforeStart = Math.max(0, lineIndex - contextLines);
      const beforeEnd = lineIndex;
      if (beforeStart < beforeEnd) {
        match.contextBefore = lines.slice(beforeStart, beforeEnd);
      }
      
      // Add context after
      const afterStart = lineIndex + 1;
      const afterEnd = Math.min(lines.length, lineIndex + 1 + contextLines);
      if (afterStart < afterEnd) {
        match.contextAfter = lines.slice(afterStart, afterEnd);
      }
    });
  }

  private async isBinaryFile(filePath: string): Promise<boolean> {
    try {
      const buffer = await readFile(filePath);
      const sample = buffer.slice(0, 8000);
      
      // Check for null bytes (common in binary files)
      for (let i = 0; i < sample.length; i++) {
        if (sample[i] === 0) {
          return true;
        }
      }
      
      // Check file extension
      const ext = extname(filePath).toLowerCase();
      const binaryExtensions = [
        '.exe', '.dll', '.so', '.dylib', '.bin', '.dat',
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico',
        '.mp3', '.mp4', '.avi', '.mov', '.wav',
        '.zip', '.tar', '.gz', '.rar', '.7z',
        '.pdf', '.doc', '.docx', '.xls', '.xlsx'
      ];
      
      return binaryExtensions.includes(ext);
    } catch (error) {
      return false;
    }
  }

  private calculateSimilarity(str1: string, str2: string): number {
    // Levenshtein distance based similarity
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }
    
    const maxLength = Math.max(str1.length, str2.length);
    return maxLength === 0 ? 1 : (maxLength - matrix[str2.length][str1.length]) / maxLength;
  }

  private tokenize(text: string): string[] {
    return text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 0);
  }

  private findBestMatchSpan(line: string, keywords: string[]): { start: number; end: number } {
    let bestStart = 0;
    let bestEnd = line.length;
    let bestScore = 0;
    
    // Try to find the span that contains the most keywords
    for (let start = 0; start < line.length; start++) {
      for (let end = start + 1; end <= line.length; end++) {
        const span = line.substring(start, end).toLowerCase();
        const score = keywords.filter(keyword => span.includes(keyword)).length;
        
        if (score > bestScore || (score === bestScore && (end - start) < (bestEnd - bestStart))) {
          bestStart = start;
          bestEnd = end;
          bestScore = score;
        }
      }
    }
    
    return { start: bestStart, end: bestEnd };
  }

  private escapeRegex(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
}

// Global advanced search instance
let globalAdvancedSearch: AdvancedSearch | null = null;

export function getAdvancedSearch(): AdvancedSearch {
  if (!globalAdvancedSearch) {
    globalAdvancedSearch = new AdvancedSearch();
  }
  return globalAdvancedSearch;
}

// Convenience functions
export async function searchText(
  rootPath: string,
  query: string,
  options: Partial<SearchOptions> = {}
): Promise<{ results: SearchResult[]; summary: SearchSummary }> {
  const search = getAdvancedSearch();
  return search.search(rootPath, { ...options, query, searchType: 'text' });
}

export async function searchRegex(
  rootPath: string,
  pattern: string,
  options: Partial<SearchOptions> = {}
): Promise<{ results: SearchResult[]; summary: SearchSummary }> {
  const search = getAdvancedSearch();
  return search.search(rootPath, { ...options, query: pattern, searchType: 'regex' });
}

export async function searchFuzzy(
  rootPath: string,
  query: string,
  options: Partial<SearchOptions> = {}
): Promise<{ results: SearchResult[]; summary: SearchSummary }> {
  const search = getAdvancedSearch();
  return search.search(rootPath, { ...options, query, searchType: 'fuzzy' });
}
