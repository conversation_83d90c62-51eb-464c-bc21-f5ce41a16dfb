// External API service for web requests and integrations
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { logger } from '../core/logger.js';
import { ArienError, ErrorCode } from '../utils/errors.js';
import { withRetry } from '../utils/retry.js';
import { LruCache } from '../utils/LruCache.js';

export interface ApiRequestConfig extends AxiosRequestConfig {
  cacheKey?: string;
  cacheTtl?: number;
  retries?: number;
  timeout?: number;
  rateLimit?: {
    requestsPerSecond: number;
    burstLimit: number;
  };
}

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  cached?: boolean;
  requestId: string;
  duration: number;
}

export interface RateLimitState {
  requests: number[];
  lastReset: number;
  blocked: boolean;
}

export class ApiService {
  private client: AxiosInstance;
  private cache = new LruCache<string, { data: any; expiresAt: number }>(1000);
  private rateLimits = new Map<string, RateLimitState>();
  private requestCounter = 0;

  constructor(baseConfig: AxiosRequestConfig = {}) {
    this.client = axios.create({
      timeout: 30000,
      headers: {
        'User-Agent': 'Arien-CLI/1.0.0',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      ...baseConfig
    });

    this.setupInterceptors();
  }

  public async get<T = any>(url: string, config: ApiRequestConfig = {}): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'GET', url });
  }

  public async post<T = any>(url: string, data?: any, config: ApiRequestConfig = {}): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'POST', url, data });
  }

  public async put<T = any>(url: string, data?: any, config: ApiRequestConfig = {}): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'PUT', url, data });
  }

  public async delete<T = any>(url: string, config: ApiRequestConfig = {}): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'DELETE', url });
  }

  public async patch<T = any>(url: string, data?: any, config: ApiRequestConfig = {}): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'PATCH', url, data });
  }

  public async request<T = any>(config: ApiRequestConfig): Promise<ApiResponse<T>> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      // Check cache first
      if (config.method === 'GET' && config.cacheKey) {
        const cached = this.getFromCache(config.cacheKey);
        if (cached) {
          logger.debug('API request served from cache', {
            requestId,
            url: config.url,
            cacheKey: config.cacheKey
          });

          return {
            data: cached.data,
            status: 200,
            statusText: 'OK',
            headers: {},
            cached: true,
            requestId,
            duration: Date.now() - startTime
          };
        }
      }

      // Check rate limits
      if (config.rateLimit) {
        await this.checkRateLimit(config.url || '', config.rateLimit);
      }

      // Make the request with retry logic
      const response = await withRetry(
        () => this.client.request(config),
        { 
          maxAttempts: config.retries || 3,
          baseDelay: 1000,
          maxDelay: 10000
        }
      );

      const apiResponse: ApiResponse<T> = {
        data: response.data,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers as Record<string, string>,
        cached: false,
        requestId,
        duration: Date.now() - startTime
      };

      // Cache successful GET requests
      if (config.method === 'GET' && config.cacheKey && response.status === 200) {
        this.setCache(config.cacheKey, response.data, config.cacheTtl || 300000); // 5 minutes default
      }

      logger.debug('API request completed', {
        requestId,
        url: config.url,
        method: config.method,
        status: response.status,
        duration: apiResponse.duration
      });

      return apiResponse;

    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error('API request failed', {
        requestId,
        url: config.url,
        method: config.method,
        error: error instanceof Error ? error.message : String(error),
        duration
      });

      if (axios.isAxiosError(error)) {
        throw new ArienError(
          ErrorCode.NETWORK_ERROR,
          `API request failed: ${error.message}`,
          {
            requestId,
            url: config.url,
            status: error.response?.status,
            statusText: error.response?.statusText,
            duration
          }
        );
      }

      throw new ArienError(
        ErrorCode.NETWORK_ERROR,
        `API request failed: ${error instanceof Error ? error.message : String(error)}`,
        { requestId, url: config.url, duration }
      );
    }
  }

  public async downloadFile(url: string, config: ApiRequestConfig = {}): Promise<Buffer> {
    const response = await this.request<ArrayBuffer>({
      ...config,
      url,
      method: 'GET',
      responseType: 'arraybuffer'
    });

    return Buffer.from(response.data);
  }

  public async uploadFile(
    url: string, 
    file: Buffer | string, 
    filename: string,
    config: ApiRequestConfig = {}
  ): Promise<ApiResponse> {
    const formData = new FormData();
    
    if (typeof file === 'string') {
      formData.append('file', new Blob([file]), filename);
    } else {
      formData.append('file', new Blob([file]), filename);
    }

    return this.request({
      ...config,
      url,
      method: 'POST',
      data: formData,
      headers: {
        ...config.headers,
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  public setDefaultHeader(name: string, value: string): void {
    this.client.defaults.headers.common[name] = value;
  }

  public removeDefaultHeader(name: string): void {
    delete this.client.defaults.headers.common[name];
  }

  public setBaseURL(baseURL: string): void {
    this.client.defaults.baseURL = baseURL;
  }

  public setTimeout(timeout: number): void {
    this.client.defaults.timeout = timeout;
  }

  public clearCache(): void {
    this.cache.clear();
    logger.debug('API cache cleared');
  }

  public getCacheStats(): { size: number; hitRate: number } {
    return {
      size: this.cache.size(),
      hitRate: 0 // Would need to track hits/misses for accurate rate
    };
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        const requestId = this.generateRequestId();
        (config as any).metadata = { requestId, startTime: Date.now() };
        
        logger.debug('API request started', {
          requestId,
          url: config.url,
          method: config.method?.toUpperCase()
        });

        return config;
      },
      (error) => {
        logger.error('API request interceptor error', { error });
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        const { requestId, startTime } = (response.config as any).metadata || {};
        const duration = startTime ? Date.now() - startTime : 0;

        logger.debug('API response received', {
          requestId,
          status: response.status,
          duration
        });

        return response;
      },
      (error) => {
        const { requestId, startTime } = error.config?.metadata || {};
        const duration = startTime ? Date.now() - startTime : 0;

        logger.error('API response error', {
          requestId,
          status: error.response?.status,
          message: error.message,
          duration
        });

        return Promise.reject(error);
      }
    );
  }

  private getFromCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && cached.expiresAt > Date.now()) {
      return cached;
    }
    
    if (cached) {
      this.cache.delete(key);
    }
    
    return null;
  }

  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      expiresAt: Date.now() + ttl
    });
  }

  private async checkRateLimit(url: string, rateLimit: { requestsPerSecond: number; burstLimit: number }): Promise<void> {
    const now = Date.now();
    const windowStart = now - 1000; // 1 second window
    
    let state = this.rateLimits.get(url);
    if (!state) {
      state = {
        requests: [],
        lastReset: now,
        blocked: false
      };
      this.rateLimits.set(url, state);
    }

    // Remove old requests outside the window
    state.requests = state.requests.filter(timestamp => timestamp > windowStart);

    // Check if we're within limits
    if (state.requests.length >= rateLimit.requestsPerSecond) {
      const oldestRequest = Math.min(...state.requests);
      const waitTime = 1000 - (now - oldestRequest);
      
      if (waitTime > 0) {
        logger.debug('Rate limit hit, waiting', { url, waitTime });
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }

    // Add current request
    state.requests.push(now);
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${++this.requestCounter}`;
  }
}

// Global API service instance
let globalApiService: ApiService | null = null;

export function getApiService(): ApiService {
  if (!globalApiService) {
    globalApiService = new ApiService();
  }
  return globalApiService;
}

export function initApiService(config?: AxiosRequestConfig): ApiService {
  globalApiService = new ApiService(config);
  return globalApiService;
}

// Convenience functions for common operations
export async function fetchJson<T = any>(url: string, config?: ApiRequestConfig): Promise<T> {
  const apiService = getApiService();
  const response = await apiService.get<T>(url, config);
  return response.data;
}

export async function postJson<T = any>(url: string, data: any, config?: ApiRequestConfig): Promise<T> {
  const apiService = getApiService();
  const response = await apiService.post<T>(url, data, config);
  return response.data;
}

export async function downloadFile(url: string, config?: ApiRequestConfig): Promise<Buffer> {
  const apiService = getApiService();
  return apiService.downloadFile(url, config);
}
