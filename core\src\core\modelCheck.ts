// Model availability and compatibility checking
import { Provider, PROVIDERS, getModelConfig, ModelConfig, getModelsByProvider } from '../config/models.js';
import { ArienRequest, RequestOptions } from './ArienRequest.js';
import { logger } from './logger.js';
import { ArienError, ErrorCode } from '../utils/errors.js';

export interface ModelAvailability {
  provider: Provider;
  model: string;
  available: boolean;
  latency?: number;
  error?: string;
  features: {
    supportsTools: boolean;
    supportsStreaming: boolean;
    maxTokens: number;
  };
  lastChecked: Date;
}

export interface ProviderStatus {
  provider: Provider;
  available: boolean;
  models: ModelAvailability[];
  error?: string;
  lastChecked: Date;
}

export interface ModelCompatibility {
  compatible: boolean;
  issues: string[];
  recommendations: string[];
}

export class ModelChecker {
  private availabilityCache = new Map<string, ModelAvailability>();
  private providerStatusCache = new Map<Provider, ProviderStatus>();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  public async checkModelAvailability(
    provider: Provider,
    model: string,
    useCache: boolean = true
  ): Promise<ModelAvailability> {
    const cacheKey = `${provider}:${model}`;
    
    // Check cache first
    if (useCache) {
      const cached = this.availabilityCache.get(cacheKey);
      if (cached && this.isCacheValid(cached.lastChecked)) {
        return cached;
      }
    }

    logger.debug('Checking model availability', { provider, model });

    const startTime = Date.now();
    let availability: ModelAvailability;

    try {
      // Get model configuration
      const modelConfig = getModelConfig(model);
      if (!modelConfig) {
        throw new Error(`Model configuration not found: ${model}`);
      }

      // Test the model with a simple request
      const request = new ArienRequest();
      const testOptions: RequestOptions = {
        provider,
        model,
        temperature: 0.1,
        maxTokens: 10,
        timeout: 10000
      };

      const testMessages = [{
        role: 'user' as const,
        content: 'Hello'
      }];

      await request.makeRequest(testMessages, testOptions);
      
      const latency = Date.now() - startTime;

      availability = {
        provider,
        model,
        available: true,
        latency,
        features: {
          supportsTools: modelConfig.supportsTools,
          supportsStreaming: modelConfig.supportsStreaming,
          maxTokens: modelConfig.maxTokens
        },
        lastChecked: new Date()
      };

      logger.debug('Model availability check successful', {
        provider,
        model,
        latency
      });
    } catch (error) {
      const latency = Date.now() - startTime;
      
      availability = {
        provider,
        model,
        available: false,
        latency,
        error: error instanceof Error ? error.message : String(error),
        features: {
          supportsTools: false,
          supportsStreaming: false,
          maxTokens: 0
        },
        lastChecked: new Date()
      };

      logger.warn('Model availability check failed', {
        provider,
        model,
        error: availability.error,
        latency
      });
    }

    // Cache the result
    this.availabilityCache.set(cacheKey, availability);
    
    return availability;
  }

  public async checkProviderStatus(
    provider: Provider,
    useCache: boolean = true
  ): Promise<ProviderStatus> {
    // Check cache first
    if (useCache) {
      const cached = this.providerStatusCache.get(provider);
      if (cached && this.isCacheValid(cached.lastChecked)) {
        return cached;
      }
    }

    logger.debug('Checking provider status', { provider });

    if (!PROVIDERS[provider as keyof typeof PROVIDERS]) {
      throw new Error(`Provider configuration not found: ${provider}`);
    }

    const providerModels = getModelsByProvider(provider as Provider);
    const modelChecks = await Promise.allSettled(
      providerModels.map((model: ModelConfig) =>
        this.checkModelAvailability(provider, model.id, false)
      )
    );

    const modelAvailabilities: ModelAvailability[] = [];
    let hasAvailableModel = false;
    let providerError: string | undefined;

    for (const result of modelChecks) {
      if (result.status === 'fulfilled') {
        modelAvailabilities.push(result.value);
        if (result.value.available) {
          hasAvailableModel = true;
        }
      } else {
        providerError = result.reason?.message || 'Unknown error';
      }
    }

    const status: ProviderStatus = {
      provider,
      available: hasAvailableModel,
      models: modelAvailabilities,
      error: hasAvailableModel ? undefined : providerError,
      lastChecked: new Date()
    };

    // Cache the result
    this.providerStatusCache.set(provider, status);

    logger.debug('Provider status check completed', {
      provider,
      available: status.available,
      modelCount: modelAvailabilities.length,
      availableModels: modelAvailabilities.filter((m: ModelAvailability) => m.available).length
    });

    return status;
  }

  public async checkAllProviders(): Promise<ProviderStatus[]> {
    logger.info('Checking all provider statuses');

    const providers = Object.keys(PROVIDERS) as Provider[];
    const statusChecks = await Promise.allSettled(
      providers.map(provider => this.checkProviderStatus(provider, false))
    );

    const statuses: ProviderStatus[] = [];
    
    for (const result of statusChecks) {
      if (result.status === 'fulfilled') {
        statuses.push(result.value);
      }
    }

    const availableProviders = statuses.filter(s => s.available).length;
    
    logger.info('All provider status checks completed', {
      totalProviders: providers.length,
      availableProviders,
      unavailableProviders: providers.length - availableProviders
    });

    return statuses;
  }

  public checkModelCompatibility(
    model: string,
    requirements: {
      needsTools?: boolean;
      needsStreaming?: boolean;
      minTokens?: number;
      maxCostPer1k?: number;
    }
  ): ModelCompatibility {
    const modelConfig = getModelConfig(model);
    if (!modelConfig) {
      return {
        compatible: false,
        issues: [`Model configuration not found: ${model}`],
        recommendations: ['Use a supported model from the available list']
      };
    }

    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check tool support
    if (requirements.needsTools && !modelConfig.supportsTools) {
      issues.push('Model does not support tool/function calling');
      recommendations.push('Use a model that supports tools, such as GPT-4 or Claude-3');
    }

    // Check streaming support
    if (requirements.needsStreaming && !modelConfig.supportsStreaming) {
      issues.push('Model does not support streaming responses');
      recommendations.push('Use a model that supports streaming for real-time responses');
    }

    // Check token limits
    if (requirements.minTokens && modelConfig.maxTokens < requirements.minTokens) {
      issues.push(`Model token limit (${modelConfig.maxTokens}) is below required minimum (${requirements.minTokens})`);
      recommendations.push('Use a model with a higher token limit for large contexts');
    }

    // Check cost requirements
    if (requirements.maxCostPer1k && modelConfig.costPer1kTokens) {
      const avgCost = (modelConfig.costPer1kTokens.input + modelConfig.costPer1kTokens.output) / 2;
      if (avgCost > requirements.maxCostPer1k) {
        issues.push(`Model cost (${avgCost}/1k tokens) exceeds budget (${requirements.maxCostPer1k}/1k tokens)`);
        recommendations.push('Consider using a more cost-effective model like DeepSeek or GPT-3.5');
      }
    }

    return {
      compatible: issues.length === 0,
      issues,
      recommendations
    };
  }

  public getBestModelForTask(
    task: 'coding' | 'analysis' | 'conversation' | 'creative',
    constraints: {
      maxCostPer1k?: number;
      needsTools?: boolean;
      needsStreaming?: boolean;
      minTokens?: number;
    } = {}
  ): string | null {
    const allModels = Object.values(PROVIDERS)
      .flatMap(provider => getModelsByProvider(provider))
      .filter((config): config is ModelConfig => config !== undefined);

    // Filter models based on constraints
    let candidateModels = allModels.filter(model => {
      if (constraints.needsTools && !model.supportsTools) return false;
      if (constraints.needsStreaming && !model.supportsStreaming) return false;
      if (constraints.minTokens && model.maxTokens < constraints.minTokens) return false;
      
      if (constraints.maxCostPer1k && model.costPer1kTokens) {
        const avgCost = (model.costPer1kTokens.input + model.costPer1kTokens.output) / 2;
        if (avgCost > constraints.maxCostPer1k) return false;
      }
      
      return true;
    });

    if (candidateModels.length === 0) {
      return null;
    }

    // Score models based on task type
    const scoredModels = candidateModels.map(model => ({
      model,
      score: this.scoreModelForTask(model, task)
    }));

    // Sort by score (highest first)
    scoredModels.sort((a, b) => b.score - a.score);

    return scoredModels[0].model.name;
  }

  public clearCache(): void {
    this.availabilityCache.clear();
    this.providerStatusCache.clear();
    logger.debug('Model availability cache cleared');
  }

  public getCacheStats(): {
    modelCacheSize: number;
    providerCacheSize: number;
    oldestEntry?: Date;
  } {
    const modelEntries = Array.from(this.availabilityCache.values());
    const providerEntries = Array.from(this.providerStatusCache.values());
    
    const allDates = [
      ...modelEntries.map(e => e.lastChecked),
      ...providerEntries.map(e => e.lastChecked)
    ];

    return {
      modelCacheSize: this.availabilityCache.size,
      providerCacheSize: this.providerStatusCache.size,
      oldestEntry: allDates.length > 0 ? new Date(Math.min(...allDates.map(d => d.getTime()))) : undefined
    };
  }

  private isCacheValid(lastChecked: Date): boolean {
    return Date.now() - lastChecked.getTime() < this.cacheTimeout;
  }

  private scoreModelForTask(model: ModelConfig, task: string): number {
    let score = 0;

    // Base score from token capacity
    score += Math.min(model.maxTokens / 1000, 100); // Up to 100 points for token capacity

    // Task-specific scoring
    switch (task) {
      case 'coding':
        if (model.supportsTools) score += 50;
        if (model.name.includes('coder') || model.name.includes('code')) score += 30;
        if (model.provider === 'deepseek') score += 20; // DeepSeek is good for coding
        break;
        
      case 'analysis':
        if (model.maxTokens >= 32000) score += 40; // Large context for analysis
        if (model.provider === 'anthropic') score += 30; // Claude is good for analysis
        break;
        
      case 'conversation':
        if (model.supportsStreaming) score += 30;
        score += 20; // All models are decent for conversation
        break;
        
      case 'creative':
        if (model.provider === 'openai') score += 30; // GPT models are creative
        if (model.name.includes('gpt-4')) score += 20;
        break;
    }

    // Cost efficiency bonus (lower cost = higher score)
    if (model.costPer1kTokens) {
      const avgCost = (model.costPer1kTokens.input + model.costPer1kTokens.output) / 2;
      score += Math.max(0, 50 - avgCost * 1000); // Bonus for cheaper models
    }

    return score;
  }
}
