#!/usr/bin/env node

// Arien CLI Setup Script
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import os from 'os';

console.log('🤖 Setting up Arien CLI...\n');

// Check Node.js version
const nodeVersion = process.version;
const requiredVersion = 'v18.0.0';

if (nodeVersion < requiredVersion) {
  console.error(`❌ Node.js ${requiredVersion} or higher is required. Current version: ${nodeVersion}`);
  process.exit(1);
}

console.log(`✅ Node.js ${nodeVersion} detected`);

// Function to run command with error handling
function runCommand(command, description) {
  try {
    console.log(`📦 ${description}...`);
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed\n`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    process.exit(1);
  }
}

// Install dependencies
runCommand('npm install', 'Installing root dependencies');
runCommand('cd core; npm install', 'Installing core dependencies');
runCommand('cd cli; npm install', 'Installing CLI dependencies');

// Build the project
runCommand('npm run build', 'Building project');

// Create configuration directory
const configDir = path.join(os.homedir(), '.arien');
if (!fs.existsSync(configDir)) {
  fs.mkdirSync(configDir, { recursive: true });
  console.log('✅ Created configuration directory');
}

// Create logs directory
const logsDir = path.join(configDir, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
  console.log('✅ Created logs directory');
}

// Create cache directory
const cacheDir = path.join(configDir, 'cache');
if (!fs.existsSync(cacheDir)) {
  fs.mkdirSync(cacheDir, { recursive: true });
  console.log('✅ Created cache directory');
}

// Make scripts executable (Unix-like systems)
if (process.platform !== 'win32') {
  try {
    execSync('chmod +x install.sh', { stdio: 'ignore' });
    execSync('chmod +x cli/dist/index.js', { stdio: 'ignore' });
    console.log('✅ Made scripts executable');
  } catch (error) {
    console.warn('⚠️  Could not make scripts executable:', error.message);
  }
}

// Test the CLI
try {
  console.log('🧪 Testing CLI...');
  execSync('node cli/dist/index.js --version', { stdio: 'pipe' });
  console.log('✅ CLI test passed\n');
} catch (error) {
  console.warn('⚠️  CLI test failed, but installation may still work\n');
}

console.log('🎉 Arien CLI setup completed successfully!\n');

console.log('Next steps:');
console.log('1. Configure your AI provider:');
console.log('   node cli/dist/index.js auth');
console.log('');
console.log('2. Test the CLI:');
console.log('   node cli/dist/index.js "Hello, how can you help me?"');
console.log('');
console.log('3. (Optional) Create global symlink:');
console.log('   npm link cli');
console.log('   Then use: arien "your message"');
console.log('');
console.log('For more information:');
console.log('   node cli/dist/index.js --help');
console.log('');
console.log('Happy coding! 🚀');
