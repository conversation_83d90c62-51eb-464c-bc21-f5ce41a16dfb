// HTTP fetch utilities with retry and error handling
import fetch, { RequestInit, Response } from 'node-fetch';
import { logger } from '../core/logger.js';
import { ArienError, ErrorCode } from './errors.js';
import { withRetry } from './retry.js';

export interface FetchOptions extends RequestInit {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  retryCondition?: (error: Error, attempt: number) => boolean;
  validateStatus?: (status: number) => boolean;
  parseResponse?: 'json' | 'text' | 'buffer' | 'stream';
  logRequests?: boolean;
}

export interface FetchResult<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  url: string;
  duration: number;
}

export class HttpClient {
  private defaultOptions: FetchOptions;

  constructor(defaultOptions: FetchOptions = {}) {
    this.defaultOptions = {
      timeout: 30000,
      retries: 3,
      retryDelay: 1000,
      validateStatus: (status) => status >= 200 && status < 300,
      parseResponse: 'json',
      logRequests: false,
      ...defaultOptions
    };
  }

  public async get<T = any>(url: string, options: FetchOptions = {}): Promise<FetchResult<T>> {
    return this.request<T>(url, { ...options, method: 'GET' });
  }

  public async post<T = any>(url: string, data?: any, options: FetchOptions = {}): Promise<FetchResult<T>> {
    return this.request<T>(url, {
      ...options,
      method: 'POST',
      body: this.serializeBody(data, options.headers as Record<string, string>)
    });
  }

  public async put<T = any>(url: string, data?: any, options: FetchOptions = {}): Promise<FetchResult<T>> {
    return this.request<T>(url, {
      ...options,
      method: 'PUT',
      body: this.serializeBody(data, options.headers as Record<string, string>)
    });
  }

  public async patch<T = any>(url: string, data?: any, options: FetchOptions = {}): Promise<FetchResult<T>> {
    return this.request<T>(url, {
      ...options,
      method: 'PATCH',
      body: this.serializeBody(data, options.headers as Record<string, string>)
    });
  }

  public async delete<T = any>(url: string, options: FetchOptions = {}): Promise<FetchResult<T>> {
    return this.request<T>(url, { ...options, method: 'DELETE' });
  }

  public async request<T = any>(url: string, options: FetchOptions = {}): Promise<FetchResult<T>> {
    const mergedOptions = { ...this.defaultOptions, ...options };
    const startTime = Date.now();

    const executeRequest = async (): Promise<FetchResult<T>> => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), mergedOptions.timeout);

      try {
        if (mergedOptions.logRequests) {
          logger.debug('HTTP request', {
            method: mergedOptions.method || 'GET',
            url,
            headers: mergedOptions.headers
          });
        }

        const response = await fetch(url, {
          ...mergedOptions,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        // Validate status
        if (mergedOptions.validateStatus && !mergedOptions.validateStatus(response.status)) {
          throw new ArienError(
            ErrorCode.HTTP_ERROR,
            `HTTP ${response.status}: ${response.statusText}`,
            { status: response.status, statusText: response.statusText, url }
          );
        }

        // Parse response
        const data = await this.parseResponse<T>(response, mergedOptions.parseResponse);

        const result: FetchResult<T> = {
          data,
          status: response.status,
          statusText: response.statusText,
          headers: this.extractHeaders(response),
          url: response.url,
          duration: Date.now() - startTime
        };

        if (mergedOptions.logRequests) {
          logger.debug('HTTP response', {
            status: response.status,
            url,
            duration: result.duration
          });
        }

        return result;
      } catch (error) {
        clearTimeout(timeoutId);
        
        if (error.name === 'AbortError') {
          throw new ArienError(
            ErrorCode.TIMEOUT_ERROR,
            `Request timeout after ${mergedOptions.timeout}ms`,
            { url, timeout: mergedOptions.timeout }
          );
        }

        throw error;
      }
    };

    // Apply retry logic if configured
    if (mergedOptions.retries && mergedOptions.retries > 0) {
      return withRetry(executeRequest, {
        maxAttempts: mergedOptions.retries,
        baseDelay: mergedOptions.retryDelay || 1000,
        retryCondition: mergedOptions.retryCondition
      });
    } else {
      return executeRequest();
    }
  }

  private serializeBody(data: any, headers: Record<string, string> = {}): string | Buffer | undefined {
    if (!data) return undefined;

    const contentType = headers['content-type'] || headers['Content-Type'];

    if (typeof data === 'string' || Buffer.isBuffer(data)) {
      return data;
    }

    if (contentType && contentType.includes('application/x-www-form-urlencoded')) {
      return new URLSearchParams(data).toString();
    }

    // Default to JSON
    return JSON.stringify(data);
  }

  private async parseResponse<T>(response: Response, parseType?: string): Promise<T> {
    switch (parseType) {
      case 'text':
        return (await response.text()) as T;
      case 'buffer':
        return (await response.buffer()) as T;
      case 'stream':
        return response.body as T;
      case 'json':
      default:
        try {
          return await response.json() as T;
        } catch (error) {
          // If JSON parsing fails, return text
          return (await response.text()) as T;
        }
    }
  }

  private extractHeaders(response: Response): Record<string, string> {
    const headers: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      headers[key] = value;
    });
    return headers;
  }
}

// Default client instance
export const httpClient = new HttpClient();

// Convenience functions
export async function get<T = any>(url: string, options: FetchOptions = {}): Promise<FetchResult<T>> {
  return httpClient.get<T>(url, options);
}

export async function post<T = any>(url: string, data?: any, options: FetchOptions = {}): Promise<FetchResult<T>> {
  return httpClient.post<T>(url, data, options);
}

export async function put<T = any>(url: string, data?: any, options: FetchOptions = {}): Promise<FetchResult<T>> {
  return httpClient.put<T>(url, data, options);
}

export async function patch<T = any>(url: string, data?: any, options: FetchOptions = {}): Promise<FetchResult<T>> {
  return httpClient.patch<T>(url, data, options);
}

export async function del<T = any>(url: string, options: FetchOptions = {}): Promise<FetchResult<T>> {
  return httpClient.delete<T>(url, options);
}

// Utility functions
export function isRetryableError(error: Error): boolean {
  if (error instanceof ArienError) {
    return error.code === ErrorCode.NETWORK_ERROR ||
           error.code === ErrorCode.TIMEOUT_ERROR ||
           (error.code === ErrorCode.HTTP_ERROR && error.metadata?.status >= 500);
  }
  
  return error.name === 'FetchError' || 
         error.name === 'AbortError' ||
         error.message.includes('ECONNRESET') ||
         error.message.includes('ENOTFOUND') ||
         error.message.includes('ECONNREFUSED');
}

export function createRetryCondition(
  maxRetries: number = 3,
  retryableStatuses: number[] = [408, 429, 500, 502, 503, 504]
): (error: Error, attempt: number) => boolean {
  return (error: Error, attempt: number) => {
    if (attempt >= maxRetries) return false;
    
    if (error instanceof ArienError && error.code === ErrorCode.HTTP_ERROR) {
      const status = error.metadata?.status;
      return status ? retryableStatuses.includes(status) : false;
    }
    
    return isRetryableError(error);
  };
}

// Rate limiting utilities
export class RateLimiter {
  private requests: number[] = [];
  
  constructor(
    private maxRequests: number,
    private windowMs: number
  ) {}

  public async waitIfNeeded(): Promise<void> {
    const now = Date.now();
    
    // Remove old requests outside the window
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    
    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.windowMs - (now - oldestRequest);
      
      if (waitTime > 0) {
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
    
    this.requests.push(now);
  }
}

// Create a rate-limited HTTP client
export function createRateLimitedClient(
  maxRequests: number,
  windowMs: number,
  options: FetchOptions = {}
): HttpClient {
  const rateLimiter = new RateLimiter(maxRequests, windowMs);
  
  return new HttpClient({
    ...options,
    // Wrap the original request method to include rate limiting
    async request(url: string, requestOptions: FetchOptions = {}) {
      await rateLimiter.waitIfNeeded();
      return httpClient.request(url, requestOptions);
    }
  } as any);
}
